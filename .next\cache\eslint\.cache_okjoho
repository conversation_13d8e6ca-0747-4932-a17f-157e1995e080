[{"C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\404\\page.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\admin\\page.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\admin-demo\\page.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\api\\create-demo-users\\route.ts": "4", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\api\\payments\\create-order\\route.ts": "5", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\api\\payments\\verify\\route.ts": "6", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\api\\track-visitor\\route.ts": "7", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\auth\\callback\\page.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\contact\\page.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\customize\\page.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\dashboard\\page.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\forgot-password\\page.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\layout.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\login\\page.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\page.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\reset-password\\page.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\success\\page.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\templates\\page.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\layout\\main-layout.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\navigation\\sidebar-nav.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\payment\\razorpay-button.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\badge.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\button.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\card.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\dialog.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\input.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\label.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\navigation-menu.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\select.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\separator.tsx": "30", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\sheet.tsx": "31", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\sidebar.tsx": "32", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\skeleton.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\slider.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\sonner.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\tabs.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\textarea.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\tooltip.tsx": "38", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\hooks\\use-mobile.ts": "39", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\hooks\\use-visitor-tracking.ts": "40", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\lib\\auth.ts": "41", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\lib\\database.types.ts": "42", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\lib\\razorpay.ts": "43", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\lib\\supabase\\client.ts": "44", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\lib\\supabase\\middleware.ts": "45", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\lib\\supabase\\server.ts": "46", "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\lib\\utils.ts": "47"}, {"size": 1729, "mtime": 1748855234366, "results": "48", "hashOfConfig": "49"}, {"size": 40783, "mtime": 1748856325894, "results": "50", "hashOfConfig": "49"}, {"size": 18624, "mtime": 1748852987590, "results": "51", "hashOfConfig": "49"}, {"size": 4116, "mtime": 1748855355536, "results": "52", "hashOfConfig": "49"}, {"size": 1880, "mtime": 1748852926223, "results": "53", "hashOfConfig": "49"}, {"size": 2838, "mtime": 1748852457700, "results": "54", "hashOfConfig": "49"}, {"size": 2369, "mtime": 1748856750606, "results": "55", "hashOfConfig": "49"}, {"size": 2726, "mtime": 1748854852632, "results": "56", "hashOfConfig": "49"}, {"size": 8791, "mtime": 1748852338985, "results": "57", "hashOfConfig": "49"}, {"size": 55227, "mtime": 1748853389897, "results": "58", "hashOfConfig": "49"}, {"size": 9579, "mtime": 1748852555742, "results": "59", "hashOfConfig": "49"}, {"size": 4722, "mtime": 1748854871665, "results": "60", "hashOfConfig": "49"}, {"size": 832, "mtime": 1748851546873, "results": "61", "hashOfConfig": "49"}, {"size": 11557, "mtime": 1748854831960, "results": "62", "hashOfConfig": "49"}, {"size": 23341, "mtime": 1748856387096, "results": "63", "hashOfConfig": "49"}, {"size": 7084, "mtime": 1748854895614, "results": "64", "hashOfConfig": "49"}, {"size": 1955, "mtime": 1748852570160, "results": "65", "hashOfConfig": "49"}, {"size": 14887, "mtime": 1748855741341, "results": "66", "hashOfConfig": "49"}, {"size": 2232, "mtime": 1748852369970, "results": "67", "hashOfConfig": "49"}, {"size": 6846, "mtime": 1748855016507, "results": "68", "hashOfConfig": "49"}, {"size": 3356, "mtime": 1748855787474, "results": "69", "hashOfConfig": "49"}, {"size": 1631, "mtime": 1748851219377, "results": "70", "hashOfConfig": "49"}, {"size": 2123, "mtime": 1748851034141, "results": "71", "hashOfConfig": "49"}, {"size": 1989, "mtime": 1748851034160, "results": "72", "hashOfConfig": "49"}, {"size": 3849, "mtime": 1748856685230, "results": "73", "hashOfConfig": "49"}, {"size": 967, "mtime": 1748851034401, "results": "74", "hashOfConfig": "49"}, {"size": 611, "mtime": 1748851663945, "results": "75", "hashOfConfig": "49"}, {"size": 6664, "mtime": 1748851034196, "results": "76", "hashOfConfig": "49"}, {"size": 6253, "mtime": 1748852148637, "results": "77", "hashOfConfig": "49"}, {"size": 699, "mtime": 1748851034389, "results": "78", "hashOfConfig": "49"}, {"size": 4090, "mtime": 1748851034216, "results": "79", "hashOfConfig": "49"}, {"size": 21633, "mtime": 1748851034366, "results": "80", "hashOfConfig": "49"}, {"size": 276, "mtime": 1748851034411, "results": "81", "hashOfConfig": "49"}, {"size": 2001, "mtime": 1748851664029, "results": "82", "hashOfConfig": "49"}, {"size": 564, "mtime": 1748852022022, "results": "83", "hashOfConfig": "49"}, {"size": 1969, "mtime": 1748851663987, "results": "84", "hashOfConfig": "49"}, {"size": 759, "mtime": 1748851697495, "results": "85", "hashOfConfig": "49"}, {"size": 1891, "mtime": 1748851034396, "results": "86", "hashOfConfig": "49"}, {"size": 565, "mtime": 1748851034406, "results": "87", "hashOfConfig": "49"}, {"size": 4962, "mtime": 1748855622015, "results": "88", "hashOfConfig": "49"}, {"size": 1048, "mtime": 1748852055974, "results": "89", "hashOfConfig": "49"}, {"size": 8508, "mtime": 1748856712481, "results": "90", "hashOfConfig": "49"}, {"size": 1035, "mtime": 1748852876615, "results": "91", "hashOfConfig": "49"}, {"size": 360, "mtime": 1748852891718, "results": "92", "hashOfConfig": "49"}, {"size": 2108, "mtime": 1748851062793, "results": "93", "hashOfConfig": "49"}, {"size": 950, "mtime": 1748852907935, "results": "94", "hashOfConfig": "49"}, {"size": 166, "mtime": 1748851007364, "results": "95", "hashOfConfig": "49"}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "10gq9jh", {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 26, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\404\\page.tsx", ["237", "238"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\admin\\page.tsx", ["239", "240", "241", "242", "243", "244"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\admin-demo\\page.tsx", ["245"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\api\\create-demo-users\\route.ts", ["246", "247", "248"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\api\\payments\\create-order\\route.ts", ["249"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\api\\payments\\verify\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\api\\track-visitor\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\auth\\callback\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\contact\\page.tsx", ["250", "251", "252"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\customize\\page.tsx", ["253", "254", "255", "256", "257", "258", "259", "260", "261", "262", "263", "264", "265", "266", "267", "268", "269", "270", "271", "272", "273", "274", "275", "276", "277", "278", "279"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\dashboard\\page.tsx", ["280", "281"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\forgot-password\\page.tsx", ["282", "283", "284", "285"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\login\\page.tsx", ["286", "287"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\page.tsx", ["288", "289", "290", "291", "292", "293", "294"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\reset-password\\page.tsx", ["295", "296", "297"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\success\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\templates\\page.tsx", ["298", "299", "300", "301", "302", "303"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\layout\\main-layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\navigation\\sidebar-nav.tsx", ["304", "305", "306", "307", "308", "309"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\payment\\razorpay-button.tsx", ["310", "311", "312"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\navigation-menu.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\separator.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\sheet.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\sidebar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\skeleton.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\slider.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\sonner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\ui\\tooltip.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\hooks\\use-mobile.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\hooks\\use-visitor-tracking.ts", ["313"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\lib\\database.types.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\lib\\razorpay.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\lib\\supabase\\client.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\lib\\supabase\\middleware.ts", ["314"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\lib\\supabase\\server.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\lib\\utils.ts", [], [], {"ruleId": "315", "severity": 2, "message": "316", "line": 4, "column": 16, "nodeType": null, "messageId": "317", "endLine": 4, "endColumn": 25}, {"ruleId": "318", "severity": 2, "message": "319", "line": 16, "column": 20, "nodeType": "320", "messageId": "321", "suggestions": "322"}, {"ruleId": "315", "severity": 2, "message": "323", "line": 21, "column": 3, "nodeType": null, "messageId": "317", "endLine": 21, "endColumn": 6}, {"ruleId": "315", "severity": 2, "message": "324", "line": 44, "column": 10, "nodeType": null, "messageId": "317", "endLine": 44, "endColumn": 14}, {"ruleId": "325", "severity": 2, "message": "326", "line": 44, "column": 36, "nodeType": "327", "messageId": "328", "endLine": 44, "endColumn": 39, "suggestions": "329"}, {"ruleId": "330", "severity": 1, "message": "331", "line": 59, "column": 6, "nodeType": "332", "endLine": 59, "endColumn": 8, "suggestions": "333"}, {"ruleId": "315", "severity": 2, "message": "334", "line": 925, "column": 9, "nodeType": null, "messageId": "317", "endLine": 925, "endColumn": 19}, {"ruleId": "325", "severity": 2, "message": "326", "line": 1159, "column": 21, "nodeType": "327", "messageId": "328", "endLine": 1159, "endColumn": 24, "suggestions": "335"}, {"ruleId": "325", "severity": 2, "message": "326", "line": 114, "column": 30, "nodeType": "327", "messageId": "328", "endLine": 114, "endColumn": 33, "suggestions": "336"}, {"ruleId": "315", "severity": 2, "message": "337", "line": 4, "column": 28, "nodeType": null, "messageId": "317", "endLine": 4, "endColumn": 35}, {"ruleId": "325", "severity": 2, "message": "326", "line": 117, "column": 23, "nodeType": "327", "messageId": "328", "endLine": 117, "endColumn": 26, "suggestions": "338"}, {"ruleId": "325", "severity": 2, "message": "326", "line": 133, "column": 19, "nodeType": "327", "messageId": "328", "endLine": 133, "endColumn": 22, "suggestions": "339"}, {"ruleId": "315", "severity": 2, "message": "340", "line": 7, "column": 48, "nodeType": null, "messageId": "317", "endLine": 7, "endColumn": 58}, {"ruleId": "318", "severity": 2, "message": "319", "line": 64, "column": 41, "nodeType": "320", "messageId": "321", "suggestions": "341"}, {"ruleId": "318", "severity": 2, "message": "319", "line": 74, "column": 45, "nodeType": "320", "messageId": "321", "suggestions": "342"}, {"ruleId": "318", "severity": 2, "message": "319", "line": 213, "column": 54, "nodeType": "320", "messageId": "321", "suggestions": "343"}, {"ruleId": "315", "severity": 2, "message": "344", "line": 14, "column": 10, "nodeType": null, "messageId": "317", "endLine": 14, "endColumn": 17}, {"ruleId": "315", "severity": 2, "message": "345", "line": 21, "column": 3, "nodeType": null, "messageId": "317", "endLine": 21, "endColumn": 7}, {"ruleId": "315", "severity": 2, "message": "346", "line": 23, "column": 3, "nodeType": null, "messageId": "317", "endLine": 23, "endColumn": 11}, {"ruleId": "315", "severity": 2, "message": "347", "line": 24, "column": 3, "nodeType": null, "messageId": "317", "endLine": 24, "endColumn": 8}, {"ruleId": "315", "severity": 2, "message": "348", "line": 31, "column": 3, "nodeType": null, "messageId": "317", "endLine": 31, "endColumn": 8}, {"ruleId": "315", "severity": 2, "message": "349", "line": 32, "column": 3, "nodeType": null, "messageId": "317", "endLine": 32, "endColumn": 7}, {"ruleId": "315", "severity": 2, "message": "350", "line": 33, "column": 3, "nodeType": null, "messageId": "317", "endLine": 33, "endColumn": 9}, {"ruleId": "315", "severity": 2, "message": "351", "line": 38, "column": 3, "nodeType": null, "messageId": "317", "endLine": 38, "endColumn": 10}, {"ruleId": "315", "severity": 2, "message": "352", "line": 42, "column": 3, "nodeType": null, "messageId": "317", "endLine": 42, "endColumn": 9}, {"ruleId": "315", "severity": 2, "message": "353", "line": 43, "column": 3, "nodeType": null, "messageId": "317", "endLine": 43, "endColumn": 9}, {"ruleId": "315", "severity": 2, "message": "354", "line": 44, "column": 3, "nodeType": null, "messageId": "317", "endLine": 44, "endColumn": 7}, {"ruleId": "315", "severity": 2, "message": "355", "line": 149, "column": 19, "nodeType": null, "messageId": "317", "endLine": 149, "endColumn": 29}, {"ruleId": "315", "severity": 2, "message": "356", "line": 161, "column": 19, "nodeType": null, "messageId": "317", "endLine": 161, "endColumn": 29}, {"ruleId": "315", "severity": 2, "message": "357", "line": 171, "column": 20, "nodeType": null, "messageId": "317", "endLine": 171, "endColumn": 31}, {"ruleId": "315", "severity": 2, "message": "358", "line": 175, "column": 20, "nodeType": null, "messageId": "317", "endLine": 175, "endColumn": 31}, {"ruleId": "315", "severity": 2, "message": "359", "line": 176, "column": 27, "nodeType": null, "messageId": "317", "endLine": 176, "endColumn": 45}, {"ruleId": "315", "severity": 2, "message": "360", "line": 179, "column": 24, "nodeType": null, "messageId": "317", "endLine": 179, "endColumn": 39}, {"ruleId": "315", "severity": 2, "message": "361", "line": 180, "column": 24, "nodeType": null, "messageId": "317", "endLine": 180, "endColumn": 39}, {"ruleId": "315", "severity": 2, "message": "362", "line": 181, "column": 25, "nodeType": null, "messageId": "317", "endLine": 181, "endColumn": 41}, {"ruleId": "325", "severity": 2, "message": "326", "line": 184, "column": 36, "nodeType": "327", "messageId": "328", "endLine": 184, "endColumn": 39, "suggestions": "363"}, {"ruleId": "330", "severity": 1, "message": "364", "line": 193, "column": 6, "nodeType": "332", "endLine": 193, "endColumn": 8, "suggestions": "365"}, {"ruleId": "315", "severity": 2, "message": "366", "line": 343, "column": 9, "nodeType": null, "messageId": "317", "endLine": 343, "endColumn": 24}, {"ruleId": "315", "severity": 2, "message": "367", "line": 358, "column": 9, "nodeType": null, "messageId": "317", "endLine": 358, "endColumn": 22}, {"ruleId": "315", "severity": 2, "message": "368", "line": 373, "column": 9, "nodeType": null, "messageId": "317", "endLine": 373, "endColumn": 24}, {"ruleId": "315", "severity": 2, "message": "369", "line": 388, "column": 9, "nodeType": null, "messageId": "317", "endLine": 388, "endColumn": 26}, {"ruleId": "315", "severity": 2, "message": "370", "line": 435, "column": 9, "nodeType": null, "messageId": "317", "endLine": 435, "endColumn": 28}, {"ruleId": "325", "severity": 2, "message": "326", "line": 513, "column": 37, "nodeType": "327", "messageId": "328", "endLine": 513, "endColumn": 40, "suggestions": "371"}, {"ruleId": "325", "severity": 2, "message": "326", "line": 20, "column": 36, "nodeType": "327", "messageId": "328", "endLine": 20, "endColumn": 39, "suggestions": "372"}, {"ruleId": "330", "severity": 1, "message": "364", "line": 30, "column": 6, "nodeType": "332", "endLine": 30, "endColumn": 8, "suggestions": "373"}, {"ruleId": "325", "severity": 2, "message": "326", "line": 33, "column": 21, "nodeType": "327", "messageId": "328", "endLine": 33, "endColumn": 24, "suggestions": "374"}, {"ruleId": "318", "severity": 2, "message": "319", "line": 51, "column": 17, "nodeType": "320", "messageId": "321", "suggestions": "375"}, {"ruleId": "318", "severity": 2, "message": "319", "line": 56, "column": 22, "nodeType": "320", "messageId": "321", "suggestions": "376"}, {"ruleId": "318", "severity": 2, "message": "319", "line": 83, "column": 44, "nodeType": "320", "messageId": "321", "suggestions": "377"}, {"ruleId": "325", "severity": 2, "message": "326", "line": 90, "column": 21, "nodeType": "327", "messageId": "328", "endLine": 90, "endColumn": 24, "suggestions": "378"}, {"ruleId": "325", "severity": 2, "message": "326", "line": 108, "column": 21, "nodeType": "327", "messageId": "328", "endLine": 108, "endColumn": 24, "suggestions": "379"}, {"ruleId": "318", "severity": 2, "message": "319", "line": 299, "column": 16, "nodeType": "320", "messageId": "321", "suggestions": "380"}, {"ruleId": "318", "severity": 2, "message": "381", "line": 313, "column": 19, "nodeType": "320", "messageId": "321", "suggestions": "382"}, {"ruleId": "318", "severity": 2, "message": "381", "line": 314, "column": 89, "nodeType": "320", "messageId": "321", "suggestions": "383"}, {"ruleId": "318", "severity": 2, "message": "381", "line": 338, "column": 19, "nodeType": "320", "messageId": "321", "suggestions": "384"}, {"ruleId": "318", "severity": 2, "message": "381", "line": 339, "column": 67, "nodeType": "320", "messageId": "321", "suggestions": "385"}, {"ruleId": "318", "severity": 2, "message": "381", "line": 363, "column": 19, "nodeType": "320", "messageId": "321", "suggestions": "386"}, {"ruleId": "318", "severity": 2, "message": "381", "line": 364, "column": 63, "nodeType": "320", "messageId": "321", "suggestions": "387"}, {"ruleId": "315", "severity": 2, "message": "388", "line": 22, "column": 9, "nodeType": null, "messageId": "317", "endLine": 22, "endColumn": 21}, {"ruleId": "315", "severity": 2, "message": "389", "line": 28, "column": 15, "nodeType": null, "messageId": "317", "endLine": 28, "endColumn": 19}, {"ruleId": "325", "severity": 2, "message": "326", "line": 69, "column": 21, "nodeType": "327", "messageId": "328", "endLine": 69, "endColumn": 24, "suggestions": "390"}, {"ruleId": "330", "severity": 1, "message": "391", "line": 32, "column": 6, "nodeType": "332", "endLine": 32, "endColumn": 8, "suggestions": "392"}, {"ruleId": "330", "severity": 1, "message": "393", "line": 36, "column": 6, "nodeType": "332", "endLine": 36, "endColumn": 55, "suggestions": "394"}, {"ruleId": "315", "severity": 2, "message": "395", "line": 92, "column": 34, "nodeType": null, "messageId": "317", "endLine": 92, "endColumn": 44}, {"ruleId": "325", "severity": 2, "message": "326", "line": 97, "column": 39, "nodeType": "327", "messageId": "328", "endLine": 97, "endColumn": 42, "suggestions": "396"}, {"ruleId": "397", "severity": 1, "message": "398", "line": 232, "column": 17, "nodeType": "399", "endLine": 236, "endColumn": 19}, {"ruleId": "315", "severity": 2, "message": "400", "line": 343, "column": 33, "nodeType": null, "messageId": "317", "endLine": 343, "endColumn": 41}, {"ruleId": "325", "severity": 2, "message": "326", "line": 56, "column": 36, "nodeType": "327", "messageId": "328", "endLine": 56, "endColumn": 39, "suggestions": "401"}, {"ruleId": "325", "severity": 2, "message": "326", "line": 57, "column": 42, "nodeType": "327", "messageId": "328", "endLine": 57, "endColumn": 45, "suggestions": "402"}, {"ruleId": "330", "severity": 1, "message": "403", "line": 76, "column": 6, "nodeType": "332", "endLine": 76, "endColumn": 8, "suggestions": "404"}, {"ruleId": "325", "severity": 2, "message": "326", "line": 95, "column": 40, "nodeType": "327", "messageId": "328", "endLine": 95, "endColumn": 43, "suggestions": "405"}, {"ruleId": "325", "severity": 2, "message": "326", "line": 120, "column": 21, "nodeType": "327", "messageId": "328", "endLine": 120, "endColumn": 24, "suggestions": "406"}, {"ruleId": "407", "severity": 2, "message": "408", "line": 128, "column": 9, "nodeType": "409", "messageId": "410", "endLine": 128, "endColumn": 12, "fix": "411"}, {"ruleId": "325", "severity": 2, "message": "326", "line": 13, "column": 21, "nodeType": "327", "messageId": "328", "endLine": 13, "endColumn": 24, "suggestions": "412"}, {"ruleId": "325", "severity": 2, "message": "326", "line": 21, "column": 15, "nodeType": "327", "messageId": "328", "endLine": 21, "endColumn": 18, "suggestions": "413"}, {"ruleId": "325", "severity": 2, "message": "326", "line": 119, "column": 48, "nodeType": "327", "messageId": "328", "endLine": 119, "endColumn": 51, "suggestions": "414"}, {"ruleId": "415", "severity": 2, "message": "416", "line": 58, "column": 3, "nodeType": "417", "messageId": "418", "endLine": 58, "endColumn": 55, "suggestions": "419"}, {"ruleId": "315", "severity": 2, "message": "420", "line": 18, "column": 48, "nodeType": null, "messageId": "317", "endLine": 18, "endColumn": 55}, "@typescript-eslint/no-unused-vars", "'ArrowLeft' is defined but never used.", "unusedVar", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["421", "422", "423", "424"], "'Eye' is defined but never used.", "'user' is assigned a value but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["425", "426"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'checkAdminAccess'. Either include it or remove the dependency array.", "ArrayExpression", ["427"], "'handleSort' is assigned a value but never used.", ["428", "429"], ["430", "431"], "'request' is defined but never used.", ["432", "433"], ["434", "435"], "'templateId' is assigned a value but never used.", ["436", "437", "438", "439"], ["440", "441", "442", "443"], ["444", "445", "446", "447"], "'getUser' is defined but never used.", "'Type' is defined but never used.", "'Settings' is defined but never used.", "'Globe' is defined but never used.", "'Image' is defined but never used.", "'Code' is defined but never used.", "'Search' is defined but never used.", "'Youtube' is defined but never used.", "'Upload' is defined but never used.", "'Trash2' is defined but never used.", "'Copy' is defined but never used.", "'setLogoUrl' is assigned a value but never used.", "'setYoutube' is assigned a value but never used.", "'setKeywords' is assigned a value but never used.", "'setCustomJS' is assigned a value but never used.", "'setGoogleAnalytics' is assigned a value but never used.", "'setMobileLayout' is assigned a value but never used.", "'setTabletLayout' is assigned a value but never used.", "'setDesktopLayout' is assigned a value but never used.", ["448", "449"], "React Hook useEffect has a missing dependency: 'checkUser'. Either include it or remove the dependency array.", ["450"], "'getNavbarStyles' is assigned a value but never used.", "'getHeroStyles' is assigned a value but never used.", "'getFooterStyles' is assigned a value but never used.", "'renderHeroContent' is assigned a value but never used.", "'renderFooterContent' is assigned a value but never used.", ["451", "452"], ["453", "454"], ["455"], ["456", "457"], ["458", "459", "460", "461"], ["462", "463", "464", "465"], ["466", "467", "468", "469"], ["470", "471"], ["472", "473"], ["474", "475", "476", "477"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["478", "479", "480", "481"], ["482", "483", "484", "485"], ["486", "487", "488", "489"], ["490", "491", "492", "493"], ["494", "495", "496", "497"], ["498", "499", "500", "501"], "'searchParams' is assigned a value but never used.", "'data' is assigned a value but never used.", ["502", "503"], "React Hook useEffect has a missing dependency: 'fetchTemplates'. Either include it or remove the dependency array.", ["504"], "React Hook useEffect has a missing dependency: 'filterAndSortTemplates'. Either include it or remove the dependency array.", ["505"], "'templateId' is defined but never used.", ["506", "507"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "'response' is defined but never used.", ["508", "509"], ["510", "511"], "React Hook useEffect has missing dependencies: 'checkUser', 'loadUserProfile', and 'supabase.auth'. Either include them or remove the dependency array.", ["512"], ["513", "514"], ["515", "516"], "prefer-const", "'nav' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "517", "text": "518"}, ["519", "520"], ["521", "522"], ["523", "524"], "@typescript-eslint/ban-ts-comment", "Use \"@ts-expect-error\" instead of \"@ts-ignore\", as \"@ts-ignore\" will do nothing if the following line is error-free.", "Line", "tsIgnoreInsteadOfExpectError", ["525"], "'options' is defined but never used.", {"messageId": "526", "data": "527", "fix": "528", "desc": "529"}, {"messageId": "526", "data": "530", "fix": "531", "desc": "532"}, {"messageId": "526", "data": "533", "fix": "534", "desc": "535"}, {"messageId": "526", "data": "536", "fix": "537", "desc": "538"}, {"messageId": "539", "fix": "540", "desc": "541"}, {"messageId": "542", "fix": "543", "desc": "544"}, {"desc": "545", "fix": "546"}, {"messageId": "539", "fix": "547", "desc": "541"}, {"messageId": "542", "fix": "548", "desc": "544"}, {"messageId": "539", "fix": "549", "desc": "541"}, {"messageId": "542", "fix": "550", "desc": "544"}, {"messageId": "539", "fix": "551", "desc": "541"}, {"messageId": "542", "fix": "552", "desc": "544"}, {"messageId": "539", "fix": "553", "desc": "541"}, {"messageId": "542", "fix": "554", "desc": "544"}, {"messageId": "526", "data": "555", "fix": "556", "desc": "529"}, {"messageId": "526", "data": "557", "fix": "558", "desc": "532"}, {"messageId": "526", "data": "559", "fix": "560", "desc": "535"}, {"messageId": "526", "data": "561", "fix": "562", "desc": "538"}, {"messageId": "526", "data": "563", "fix": "564", "desc": "529"}, {"messageId": "526", "data": "565", "fix": "566", "desc": "532"}, {"messageId": "526", "data": "567", "fix": "568", "desc": "535"}, {"messageId": "526", "data": "569", "fix": "570", "desc": "538"}, {"messageId": "526", "data": "571", "fix": "572", "desc": "529"}, {"messageId": "526", "data": "573", "fix": "574", "desc": "532"}, {"messageId": "526", "data": "575", "fix": "576", "desc": "535"}, {"messageId": "526", "data": "577", "fix": "578", "desc": "538"}, {"messageId": "539", "fix": "579", "desc": "541"}, {"messageId": "542", "fix": "580", "desc": "544"}, {"desc": "581", "fix": "582"}, {"messageId": "539", "fix": "583", "desc": "541"}, {"messageId": "542", "fix": "584", "desc": "544"}, {"messageId": "539", "fix": "585", "desc": "541"}, {"messageId": "542", "fix": "586", "desc": "544"}, {"desc": "581", "fix": "587"}, {"messageId": "539", "fix": "588", "desc": "541"}, {"messageId": "542", "fix": "589", "desc": "544"}, {"messageId": "526", "data": "590", "fix": "591", "desc": "529"}, {"messageId": "526", "data": "592", "fix": "593", "desc": "532"}, {"messageId": "526", "data": "594", "fix": "595", "desc": "535"}, {"messageId": "526", "data": "596", "fix": "597", "desc": "538"}, {"messageId": "526", "data": "598", "fix": "599", "desc": "529"}, {"messageId": "526", "data": "600", "fix": "601", "desc": "532"}, {"messageId": "526", "data": "602", "fix": "603", "desc": "535"}, {"messageId": "526", "data": "604", "fix": "605", "desc": "538"}, {"messageId": "526", "data": "606", "fix": "607", "desc": "529"}, {"messageId": "526", "data": "608", "fix": "609", "desc": "532"}, {"messageId": "526", "data": "610", "fix": "611", "desc": "535"}, {"messageId": "526", "data": "612", "fix": "613", "desc": "538"}, {"messageId": "539", "fix": "614", "desc": "541"}, {"messageId": "542", "fix": "615", "desc": "544"}, {"messageId": "539", "fix": "616", "desc": "541"}, {"messageId": "542", "fix": "617", "desc": "544"}, {"messageId": "526", "data": "618", "fix": "619", "desc": "529"}, {"messageId": "526", "data": "620", "fix": "621", "desc": "532"}, {"messageId": "526", "data": "622", "fix": "623", "desc": "535"}, {"messageId": "526", "data": "624", "fix": "625", "desc": "538"}, {"messageId": "526", "data": "626", "fix": "627", "desc": "628"}, {"messageId": "526", "data": "629", "fix": "630", "desc": "631"}, {"messageId": "526", "data": "632", "fix": "633", "desc": "634"}, {"messageId": "526", "data": "635", "fix": "636", "desc": "637"}, {"messageId": "526", "data": "638", "fix": "639", "desc": "628"}, {"messageId": "526", "data": "640", "fix": "641", "desc": "631"}, {"messageId": "526", "data": "642", "fix": "643", "desc": "634"}, {"messageId": "526", "data": "644", "fix": "645", "desc": "637"}, {"messageId": "526", "data": "646", "fix": "647", "desc": "628"}, {"messageId": "526", "data": "648", "fix": "649", "desc": "631"}, {"messageId": "526", "data": "650", "fix": "651", "desc": "634"}, {"messageId": "526", "data": "652", "fix": "653", "desc": "637"}, {"messageId": "526", "data": "654", "fix": "655", "desc": "628"}, {"messageId": "526", "data": "656", "fix": "657", "desc": "631"}, {"messageId": "526", "data": "658", "fix": "659", "desc": "634"}, {"messageId": "526", "data": "660", "fix": "661", "desc": "637"}, {"messageId": "526", "data": "662", "fix": "663", "desc": "628"}, {"messageId": "526", "data": "664", "fix": "665", "desc": "631"}, {"messageId": "526", "data": "666", "fix": "667", "desc": "634"}, {"messageId": "526", "data": "668", "fix": "669", "desc": "637"}, {"messageId": "526", "data": "670", "fix": "671", "desc": "628"}, {"messageId": "526", "data": "672", "fix": "673", "desc": "631"}, {"messageId": "526", "data": "674", "fix": "675", "desc": "634"}, {"messageId": "526", "data": "676", "fix": "677", "desc": "637"}, {"messageId": "539", "fix": "678", "desc": "541"}, {"messageId": "542", "fix": "679", "desc": "544"}, {"desc": "680", "fix": "681"}, {"desc": "682", "fix": "683"}, {"messageId": "539", "fix": "684", "desc": "541"}, {"messageId": "542", "fix": "685", "desc": "544"}, {"messageId": "539", "fix": "686", "desc": "541"}, {"messageId": "542", "fix": "687", "desc": "544"}, {"messageId": "539", "fix": "688", "desc": "541"}, {"messageId": "542", "fix": "689", "desc": "544"}, {"desc": "690", "fix": "691"}, {"messageId": "539", "fix": "692", "desc": "541"}, {"messageId": "542", "fix": "693", "desc": "544"}, {"messageId": "539", "fix": "694", "desc": "541"}, {"messageId": "542", "fix": "695", "desc": "544"}, [2759, 2784], "const nav = [...navigation]", {"messageId": "539", "fix": "696", "desc": "541"}, {"messageId": "542", "fix": "697", "desc": "544"}, {"messageId": "539", "fix": "698", "desc": "541"}, {"messageId": "542", "fix": "699", "desc": "544"}, {"messageId": "539", "fix": "700", "desc": "541"}, {"messageId": "542", "fix": "701", "desc": "544"}, {"messageId": "702", "fix": "703", "desc": "704"}, "replaceWithAlt", {"alt": "705"}, {"range": "706", "text": "707"}, "Replace with `&apos;`.", {"alt": "708"}, {"range": "709", "text": "710"}, "Replace with `&lsquo;`.", {"alt": "711"}, {"range": "712", "text": "713"}, "Replace with `&#39;`.", {"alt": "714"}, {"range": "715", "text": "716"}, "Replace with `&rsquo;`.", "suggestUnknown", {"range": "717", "text": "718"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "719", "text": "720"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [checkAdminAccess]", {"range": "721", "text": "722"}, {"range": "723", "text": "718"}, {"range": "724", "text": "720"}, {"range": "725", "text": "718"}, {"range": "726", "text": "720"}, {"range": "727", "text": "718"}, {"range": "728", "text": "720"}, {"range": "729", "text": "718"}, {"range": "730", "text": "720"}, {"alt": "705"}, {"range": "731", "text": "732"}, {"alt": "708"}, {"range": "733", "text": "734"}, {"alt": "711"}, {"range": "735", "text": "736"}, {"alt": "714"}, {"range": "737", "text": "738"}, {"alt": "705"}, {"range": "739", "text": "740"}, {"alt": "708"}, {"range": "741", "text": "742"}, {"alt": "711"}, {"range": "743", "text": "744"}, {"alt": "714"}, {"range": "745", "text": "746"}, {"alt": "705"}, {"range": "747", "text": "748"}, {"alt": "708"}, {"range": "749", "text": "750"}, {"alt": "711"}, {"range": "751", "text": "752"}, {"alt": "714"}, {"range": "753", "text": "754"}, {"range": "755", "text": "718"}, {"range": "756", "text": "720"}, "Update the dependencies array to be: [checkUser]", {"range": "757", "text": "758"}, {"range": "759", "text": "718"}, {"range": "760", "text": "720"}, {"range": "761", "text": "718"}, {"range": "762", "text": "720"}, {"range": "763", "text": "758"}, {"range": "764", "text": "718"}, {"range": "765", "text": "720"}, {"alt": "705"}, {"range": "766", "text": "767"}, {"alt": "708"}, {"range": "768", "text": "769"}, {"alt": "711"}, {"range": "770", "text": "771"}, {"alt": "714"}, {"range": "772", "text": "773"}, {"alt": "705"}, {"range": "774", "text": "775"}, {"alt": "708"}, {"range": "776", "text": "777"}, {"alt": "711"}, {"range": "778", "text": "779"}, {"alt": "714"}, {"range": "780", "text": "781"}, {"alt": "705"}, {"range": "782", "text": "783"}, {"alt": "708"}, {"range": "784", "text": "785"}, {"alt": "711"}, {"range": "786", "text": "787"}, {"alt": "714"}, {"range": "788", "text": "789"}, {"range": "790", "text": "718"}, {"range": "791", "text": "720"}, {"range": "792", "text": "718"}, {"range": "793", "text": "720"}, {"alt": "705"}, {"range": "794", "text": "795"}, {"alt": "708"}, {"range": "796", "text": "797"}, {"alt": "711"}, {"range": "798", "text": "799"}, {"alt": "714"}, {"range": "800", "text": "801"}, {"alt": "802"}, {"range": "803", "text": "804"}, "Replace with `&quot;`.", {"alt": "805"}, {"range": "806", "text": "807"}, "Replace with `&ldquo;`.", {"alt": "808"}, {"range": "809", "text": "810"}, "Replace with `&#34;`.", {"alt": "811"}, {"range": "812", "text": "813"}, "Replace with `&rdquo;`.", {"alt": "802"}, {"range": "814", "text": "815"}, {"alt": "805"}, {"range": "816", "text": "817"}, {"alt": "808"}, {"range": "818", "text": "819"}, {"alt": "811"}, {"range": "820", "text": "821"}, {"alt": "802"}, {"range": "822", "text": "823"}, {"alt": "805"}, {"range": "824", "text": "825"}, {"alt": "808"}, {"range": "826", "text": "827"}, {"alt": "811"}, {"range": "828", "text": "829"}, {"alt": "802"}, {"range": "830", "text": "831"}, {"alt": "805"}, {"range": "832", "text": "833"}, {"alt": "808"}, {"range": "834", "text": "835"}, {"alt": "811"}, {"range": "836", "text": "837"}, {"alt": "802"}, {"range": "838", "text": "839"}, {"alt": "805"}, {"range": "840", "text": "841"}, {"alt": "808"}, {"range": "842", "text": "843"}, {"alt": "811"}, {"range": "844", "text": "845"}, {"alt": "802"}, {"range": "846", "text": "847"}, {"alt": "805"}, {"range": "848", "text": "849"}, {"alt": "808"}, {"range": "850", "text": "851"}, {"alt": "811"}, {"range": "852", "text": "853"}, {"range": "854", "text": "718"}, {"range": "855", "text": "720"}, "Update the dependencies array to be: [fetchTemplates]", {"range": "856", "text": "857"}, "Update the dependencies array to be: [templates, searchTerm, selectedCategory, sortBy, filterAndSortTemplates]", {"range": "858", "text": "859"}, {"range": "860", "text": "718"}, {"range": "861", "text": "720"}, {"range": "862", "text": "718"}, {"range": "863", "text": "720"}, {"range": "864", "text": "718"}, {"range": "865", "text": "720"}, "Update the dependencies array to be: [checkUser, loadUserProfile, supabase.auth]", {"range": "866", "text": "867"}, {"range": "868", "text": "718"}, {"range": "869", "text": "720"}, {"range": "870", "text": "718"}, {"range": "871", "text": "720"}, {"range": "872", "text": "718"}, {"range": "873", "text": "720"}, {"range": "874", "text": "718"}, {"range": "875", "text": "720"}, {"range": "876", "text": "718"}, {"range": "877", "text": "720"}, "replaceTsIgnoreWithTsExpectError", {"range": "878", "text": "879"}, "Replace \"@ts-ignore\" with \"@ts-expect-error\".", "&apos;", [802, 871], "\n            You don&apos;t have permission to access this page\n          ", "&lsquo;", [802, 871], "\n            You don&lsquo;t have permission to access this page\n          ", "&#39;", [802, 871], "\n            You don&#39;t have permission to access this page\n          ", "&rsquo;", [802, 871], "\n            You don&rsquo;t have permission to access this page\n          ", [1626, 1629], "unknown", [1626, 1629], "never", [2246, 2248], "[checkAdminAccess]", [37165, 37168], [37165, 37168], [2903, 2906], [2903, 2906], [3532, 3535], [3532, 3535], [3888, 3891], [3888, 3891], [1942, 2038], "\n          Get in touch with our team. We&apos;re here to help you create amazing templates.\n        ", [1942, 2038], "\n          Get in touch with our team. We&lsquo;re here to help you create amazing templates.\n        ", [1942, 2038], "\n          Get in touch with our team. We&#39;re here to help you create amazing templates.\n        ", [1942, 2038], "\n          Get in touch with our team. We&rsquo;re here to help you create amazing templates.\n        ", [2256, 2353], "\n              Fill out the form below and we&apos;ll get back to you as soon as possible\n            ", [2256, 2353], "\n              Fill out the form below and we&lsquo;ll get back to you as soon as possible\n            ", [2256, 2353], "\n              Fill out the form below and we&#39;ll get back to you as soon as possible\n            ", [2256, 2353], "\n              Fill out the form below and we&rsquo;ll get back to you as soon as possible\n            ", [7797, 7823], "What&apos;s your response time?", [7797, 7823], "What&lsquo;s your response time?", [7797, 7823], "What&#39;s your response time?", [7797, 7823], "What&rsquo;s your response time?", [8085, 8088], [8085, 8088], [8338, 8340], "[checkUser]", [17174, 17177], [17174, 17177], [782, 785], [782, 785], [1083, 1085], [1062, 1065], [1062, 1065], [1784, 1835], "\n              We&apos;ve sent a password reset link to ", [1784, 1835], "\n              We&lsquo;ve sent a password reset link to ", [1784, 1835], "\n              We&#39;ve sent a password reset link to ", [1784, 1835], "\n              We&rsquo;ve sent a password reset link to ", [2033, 2084], "Didn&apos;t receive the email? Check your spam folder or", [2033, 2084], "Didn&lsquo;t receive the email? Check your spam folder or", [2033, 2084], "Didn&#39;t receive the email? Check your spam folder or", [2033, 2084], "Didn&rsquo;t receive the email? Check your spam folder or", [2948, 3045], "\n            Enter your email address and we&apos;ll send you a link to reset your password\n          ", [2948, 3045], "\n            Enter your email address and we&lsquo;ll send you a link to reset your password\n          ", [2948, 3045], "\n            Enter your email address and we&#39;ll send you a link to reset your password\n          ", [2948, 3045], "\n            Enter your email address and we&rsquo;ll send you a link to reset your password\n          ", [2717, 2720], [2717, 2720], [3172, 3175], [3172, 3175], [12037, 12128], "\n            Don&apos;t just take our word for it - hear from our satisfied customers\n          ", [12037, 12128], "\n            Don&lsquo;t just take our word for it - hear from our satisfied customers\n          ", [12037, 12128], "\n            Don&#39;t just take our word for it - hear from our satisfied customers\n          ", [12037, 12128], "\n            Don&rsquo;t just take our word for it - hear from our satisfied customers\n          ", "&quot;", [12647, 12858], "\n                  &quot;KaleidoneX made it incredibly easy to create a professional website for my business.\n                  The templates are beautiful and the customization options are endless!\"\n                ", "&ldquo;", [12647, 12858], "\n                  &ldquo;KaleidoneX made it incredibly easy to create a professional website for my business.\n                  The templates are beautiful and the customization options are endless!\"\n                ", "&#34;", [12647, 12858], "\n                  &#34;KaleidoneX made it incredibly easy to create a professional website for my business.\n                  The templates are beautiful and the customization options are endless!\"\n                ", "&rdquo;", [12647, 12858], "\n                  &rdquo;KaleidoneX made it incredibly easy to create a professional website for my business.\n                  The templates are beautiful and the customization options are endless!\"\n                ", [12647, 12858], "\n                  \"KaleidoneX made it incredibly easy to create a professional website for my business.\n                  The templates are beautiful and the customization options are endless!&quot;\n                ", [12647, 12858], "\n                  \"KaleidoneX made it incredibly easy to create a professional website for my business.\n                  The templates are beautiful and the customization options are endless!&ldquo;\n                ", [12647, 12858], "\n                  \"KaleidoneX made it incredibly easy to create a professional website for my business.\n                  The templates are beautiful and the customization options are endless!&#34;\n                ", [12647, 12858], "\n                  \"KaleidoneX made it incredibly easy to create a professional website for my business.\n                  The templates are beautiful and the customization options are endless!&rdquo;\n                ", [13862, 14037], "\n                  &quot;As a designer, I appreciate the attention to detail in every template.\n                  The live preview feature saves me hours of work!\"\n                ", [13862, 14037], "\n                  &ldquo;As a designer, I appreciate the attention to detail in every template.\n                  The live preview feature saves me hours of work!\"\n                ", [13862, 14037], "\n                  &#34;As a designer, I appreciate the attention to detail in every template.\n                  The live preview feature saves me hours of work!\"\n                ", [13862, 14037], "\n                  &rdquo;As a designer, I appreciate the attention to detail in every template.\n                  The live preview feature saves me hours of work!\"\n                ", [13862, 14037], "\n                  \"As a designer, I appreciate the attention to detail in every template.\n                  The live preview feature saves me hours of work!&quot;\n                ", [13862, 14037], "\n                  \"As a designer, I appreciate the attention to detail in every template.\n                  The live preview feature saves me hours of work!&ldquo;\n                ", [13862, 14037], "\n                  \"As a designer, I appreciate the attention to detail in every template.\n                  The live preview feature saves me hours of work!&#34;\n                ", [13862, 14037], "\n                  \"As a designer, I appreciate the attention to detail in every template.\n                  The live preview feature saves me hours of work!&rdquo;\n                ", [15035, 15205], "\n                  &quot;The customer support is amazing! They helped me customize my template\n                  exactly how I wanted it. Highly recommended!\"\n                ", [15035, 15205], "\n                  &ldquo;The customer support is amazing! They helped me customize my template\n                  exactly how I wanted it. Highly recommended!\"\n                ", [15035, 15205], "\n                  &#34;The customer support is amazing! They helped me customize my template\n                  exactly how I wanted it. Highly recommended!\"\n                ", [15035, 15205], "\n                  &rdquo;The customer support is amazing! They helped me customize my template\n                  exactly how I wanted it. Highly recommended!\"\n                ", [15035, 15205], "\n                  \"The customer support is amazing! They helped me customize my template\n                  exactly how I wanted it. Highly recommended!&quot;\n                ", [15035, 15205], "\n                  \"The customer support is amazing! They helped me customize my template\n                  exactly how I wanted it. Highly recommended!&ldquo;\n                ", [15035, 15205], "\n                  \"The customer support is amazing! They helped me customize my template\n                  exactly how I wanted it. Highly recommended!&#34;\n                ", [15035, 15205], "\n                  \"The customer support is amazing! They helped me customize my template\n                  exactly how I wanted it. Highly recommended!&rdquo;\n                ", [2095, 2098], [2095, 2098], [1399, 1401], "[fetchTemplates]", [1458, 1507], "[templates, searchTerm, selectedCategory, sortBy, filterAndSortTemplates]", [3116, 3119], [3116, 3119], [1039, 1042], [1039, 1042], [1091, 1094], [1091, 1094], [1594, 1596], "[checkUser, loadUserProfile, supabase.auth]", [1991, 1994], [1991, 1994], [2569, 2572], [2569, 2572], [374, 377], [374, 377], [513, 516], [513, 516], [3006, 3009], [3006, 3009], [1854, 1906], "// @ts-expect-error - navigator.connection is experimental"]