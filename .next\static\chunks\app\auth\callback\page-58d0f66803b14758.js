(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[769],{447:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>l});var a=s(5155),o=s(2115),n=s(5695),t=s(2643),i=s(6671);function l(){let e=(0,n.useRouter)(),r=(0,t.U)();return(0,o.useEffect)(()=>{(async()=>{try{var s,a,o;let{data:n,error:t}=await r.auth.getSession();if(t){console.error("Auth callback error:",t),i.oR.error("Authentication failed"),e.push("/login");return}if(null==(s=n.session)?void 0:s.user){let s=n.session.user,{data:t,error:l}=await r.from("profiles").select("role, full_name").eq("id",s.id).single();if(l&&"PGRST116"===l.code){let{error:n}=await r.from("profiles").insert({id:s.id,full_name:(null==(a=s.user_metadata)?void 0:a.full_name)||(null==(o=s.email)?void 0:o.split("@")[0])||"User",role:"user"});n&&console.error("Profile creation error:",n),i.oR.success("Welcome! Your account has been created."),e.push("/dashboard")}else t?(i.oR.success("Welcome back".concat(t.full_name?", ".concat(t.full_name):"","!")),"admin"===t.role?e.push("/admin"):e.push("/dashboard")):(i.oR.error("Failed to load profile"),e.push("/login"))}else e.push("/login")}catch(r){console.error("Callback handling error:",r),i.oR.error("Authentication failed"),e.push("/login")}})()},[e,r]),(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"Completing authentication..."}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Please wait while we sign you in."})]})})}},1312:(e,r,s)=>{Promise.resolve().then(s.bind(s,447))},2643:(e,r,s)=>{"use strict";s.d(r,{U:()=>o});var a=s(1935);function o(){let e="https://aovrwjzhqrgbdhszdowg.supabase.co",r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFvdnJ3anpocXJnYmRoc3pkb3dnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4NTI5MjEsImV4cCI6MjA2NDQyODkyMX0.9TWCiuDturnqdOSlDeOWVroegkTM7Nra-W2LUoyGDSs";if(!e||!r)throw Error("Missing Supabase environment variables. Please check your .env.local file.");return(0,a.createBrowserClient)(e,r)}},5695:(e,r,s)=>{"use strict";var a=s(8999);s.o(a,"usePathname")&&s.d(r,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(r,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(r,{useSearchParams:function(){return a.useSearchParams}})}},e=>{var r=r=>e(e.s=r);e.O(0,[671,935,441,684,358],()=>r(1312)),_N_E=e.O()}]);