"use client"

import { useEffect, useState } from 'react'
import { usePathname } from 'next/navigation'

// Generate a session ID that persists for the browser session
const getSessionId = () => {
  if (typeof window === 'undefined') return 'server'

  let sessionId = sessionStorage.getItem('visitor_session_id')
  if (!sessionId) {
    sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    sessionStorage.setItem('visitor_session_id', sessionId)
  }
  return sessionId
}

// Detect device type
const getDeviceType = () => {
  if (typeof window === 'undefined') return 'unknown'

  const userAgent = navigator.userAgent.toLowerCase()
  if (/tablet|ipad|playbook|silk/.test(userAgent)) return 'tablet'
  if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/.test(userAgent)) return 'mobile'
  return 'desktop'
}

// Detect browser
const getBrowser = () => {
  if (typeof window === 'undefined') return 'unknown'

  const userAgent = navigator.userAgent
  if (userAgent.includes('Chrome')) return 'Chrome'
  if (userAgent.includes('Firefox')) return 'Firefox'
  if (userAgent.includes('Safari')) return 'Safari'
  if (userAgent.includes('Edge')) return 'Edge'
  if (userAgent.includes('Opera')) return 'Opera'
  return 'Other'
}

// Detect operating system
const getOperatingSystem = () => {
  if (typeof window === 'undefined') return 'unknown'

  const userAgent = navigator.userAgent
  if (userAgent.includes('Windows')) return 'Windows'
  if (userAgent.includes('Mac')) return 'macOS'
  if (userAgent.includes('Linux')) return 'Linux'
  if (userAgent.includes('Android')) return 'Android'
  if (userAgent.includes('iOS')) return 'iOS'
  return 'Other'
}

// Get connection type (if available)
const getConnectionType = () => {
  if (typeof window === 'undefined') return 'unknown'

  // @ts-ignore - navigator.connection is experimental
  const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection
  return connection?.effectiveType || 'unknown'
}

// Get approximate location (using timezone)
const getLocationInfo = () => {
  if (typeof window === 'undefined') return { country: 'unknown', city: 'unknown' }

  try {
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
    // Simple mapping of timezones to countries (basic implementation)
    const timezoneToCountry: Record<string, string> = {
      'America/New_York': 'United States',
      'America/Los_Angeles': 'United States',
      'Europe/London': 'United Kingdom',
      'Europe/Paris': 'France',
      'Europe/Berlin': 'Germany',
      'Asia/Tokyo': 'Japan',
      'Asia/Shanghai': 'China',
      'Asia/Kolkata': 'India',
      'Australia/Sydney': 'Australia',
    }

    const country = timezoneToCountry[timezone] || 'Unknown'
    const city = timezone.split('/')[1]?.replace('_', ' ') || 'Unknown'

    return { country, city }
  } catch {
    return { country: 'unknown', city: 'unknown' }
  }
}

export function useVisitorTracking() {
  const pathname = usePathname()
  const [pageLoadTime, setPageLoadTime] = useState<number | null>(null)

  useEffect(() => {
    // Measure page load time
    const startTime = performance.now()

    const handleLoad = () => {
      const loadTime = Math.round(performance.now() - startTime)
      setPageLoadTime(loadTime)
    }

    if (document.readyState === 'complete') {
      handleLoad()
    } else {
      window.addEventListener('load', handleLoad)
      return () => window.removeEventListener('load', handleLoad)
    }
  }, [])

  useEffect(() => {
    const trackVisitor = async () => {
      try {
        if (typeof window === 'undefined') return

        const { country, city } = getLocationInfo()

        const trackingData = {
          path: pathname,
          userAgent: navigator.userAgent,
          referrer: document.referrer,
          screenResolution: `${screen.width}x${screen.height}`,
          language: navigator.language,
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          sessionId: getSessionId(),
          deviceType: getDeviceType(),
          browser: getBrowser(),
          os: getOperatingSystem(),
          country,
          city,
          pageTitle: document.title,
          loadTime: pageLoadTime,
          connectionType: getConnectionType(),
          timestamp: new Date().toISOString(),
        }

        await fetch('/api/track-visitor', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(trackingData),
        })
      } catch (error) {
        // Silently fail - visitor tracking shouldn't break the app
        console.warn('Failed to track visitor:', error)
      }
    }

    // Add a small delay to ensure page is fully loaded
    const timer = setTimeout(trackVisitor, 1000)
    return () => clearTimeout(timer)
  }, [pathname, pageLoadTime])
}
