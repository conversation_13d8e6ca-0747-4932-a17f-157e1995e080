-- Enhanced Templates Table Migration
-- Run this in Supabase SQL Editor

-- First, let's add all the new columns to the existing templates table
ALTER TABLE templates 
ADD COLUMN IF NOT EXISTS slug TEXT,
ADD COLUMN IF NOT EXISTS long_description TEXT,
ADD COLUMN IF NOT EXISTS original_price INTEGER,
ADD COLUMN IF NOT EXISTS discount_percentage INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS demo_url TEXT,
ADD COLUMN IF NOT EXISTS download_url TEXT,
ADD COLUMN IF NOT EXISTS version TEXT DEFAULT '1.0.0',
ADD COLUMN IF NOT EXISTS features TEXT[],
ADD COLUMN IF NOT EXISTS tech_stack TEXT[],
ADD COLUMN IF NOT EXISTS difficulty_level TEXT DEFAULT 'beginner',
ADD COLUMN IF NOT EXISTS estimated_time TEXT,
ADD COLUMN IF NOT EXISTS license_type TEXT DEFAULT 'standard',
ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS is_premium BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS is_free BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS downloads_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS views_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS rating DECIMAL(2,1) DEFAULT 0.0,
ADD COLUMN IF NOT EXISTS reviews_count INTEGER DEFAULT 0;

-- Add constraints for better data integrity
ALTER TABLE templates 
ADD CONSTRAINT check_price_positive CHECK (price >= 0),
ADD CONSTRAINT check_original_price_positive CHECK (original_price IS NULL OR original_price >= 0),
ADD CONSTRAINT check_discount_percentage CHECK (discount_percentage >= 0 AND discount_percentage <= 100),
ADD CONSTRAINT check_difficulty_level CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
ADD CONSTRAINT check_license_type CHECK (license_type IN ('standard', 'extended', 'commercial')),
ADD CONSTRAINT check_rating CHECK (rating >= 0.0 AND rating <= 5.0);

-- Create unique index on slug for SEO-friendly URLs
CREATE UNIQUE INDEX IF NOT EXISTS templates_slug_idx ON templates(slug) WHERE slug IS NOT NULL;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS templates_category_idx ON templates(category);
CREATE INDEX IF NOT EXISTS templates_is_featured_idx ON templates(is_featured) WHERE is_featured = true;
CREATE INDEX IF NOT EXISTS templates_is_premium_idx ON templates(is_premium) WHERE is_premium = true;
CREATE INDEX IF NOT EXISTS templates_is_free_idx ON templates(is_free) WHERE is_free = true;
CREATE INDEX IF NOT EXISTS templates_downloads_count_idx ON templates(downloads_count DESC);
CREATE INDEX IF NOT EXISTS templates_views_count_idx ON templates(views_count DESC);
CREATE INDEX IF NOT EXISTS templates_rating_idx ON templates(rating DESC);
CREATE INDEX IF NOT EXISTS templates_created_at_idx ON templates(created_at DESC);

-- Insert some sample popular templates with enhanced data
INSERT INTO templates (
  title, 
  description, 
  long_description,
  price, 
  original_price,
  discount_percentage,
  category, 
  preview_image, 
  preview_url,
  demo_url,
  slug,
  features,
  tech_stack,
  difficulty_level,
  estimated_time,
  license_type,
  is_featured,
  is_premium,
  downloads_count,
  views_count,
  rating
) VALUES 
(
  'Modern Business Landing Page',
  'Professional landing page template perfect for startups and businesses',
  'A comprehensive business landing page template featuring modern design, responsive layout, and conversion-optimized sections. Includes hero section, features, testimonials, pricing, and contact forms. Perfect for SaaS companies, startups, and professional services.',
  1999,
  2999,
  33,
  'Business',
  'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop',
  'https://business-template-demo.vercel.app',
  'https://business-template-demo.vercel.app',
  'modern-business-landing-page',
  ARRAY['Responsive Design', 'Dark Mode', 'SEO Optimized', 'Contact Forms', 'Testimonials', 'Pricing Tables'],
  ARRAY['Next.js 15', 'TypeScript', 'Tailwind CSS', 'Framer Motion', 'React Hook Form'],
  'intermediate',
  '2-3 hours',
  'standard',
  true,
  false,
  245,
  1250,
  4.8
),
(
  'Creative Portfolio Showcase',
  'Stunning portfolio template for designers and creative professionals',
  'Showcase your creative work with this beautiful portfolio template. Features smooth animations, project galleries, about section, and contact integration. Perfect for designers, photographers, artists, and creative agencies.',
  1499,
  1999,
  25,
  'Portfolio',
  'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop',
  'https://portfolio-template-demo.vercel.app',
  'https://portfolio-template-demo.vercel.app',
  'creative-portfolio-showcase',
  ARRAY['Image Gallery', 'Smooth Animations', 'Project Showcase', 'Contact Integration', 'Mobile Optimized'],
  ARRAY['Next.js 15', 'TypeScript', 'Tailwind CSS', 'Framer Motion', 'Supabase'],
  'beginner',
  '1-2 hours',
  'standard',
  true,
  true,
  189,
  890,
  4.9
),
(
  'E-commerce Store Template',
  'Complete e-commerce solution with shopping cart and payment integration',
  'Full-featured e-commerce template with product catalog, shopping cart, user authentication, payment processing, and admin dashboard. Includes inventory management, order tracking, and customer reviews.',
  2999,
  3999,
  25,
  'E-commerce',
  'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&h=600&fit=crop',
  'https://ecommerce-template-demo.vercel.app',
  'https://ecommerce-template-demo.vercel.app',
  'ecommerce-store-template',
  ARRAY['Shopping Cart', 'Payment Integration', 'User Authentication', 'Admin Dashboard', 'Inventory Management', 'Order Tracking'],
  ARRAY['Next.js 15', 'TypeScript', 'Tailwind CSS', 'Supabase', 'Stripe', 'Razorpay'],
  'advanced',
  '4-6 hours',
  'extended',
  true,
  true,
  156,
  720,
  4.7
),
(
  'Restaurant & Food Delivery',
  'Beautiful restaurant website with online ordering system',
  'Elegant restaurant template with menu showcase, online ordering, table reservations, and delivery integration. Features beautiful food photography layouts, chef profiles, and customer reviews.',
  1799,
  2299,
  22,
  'Restaurant',
  'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=800&h=600&fit=crop',
  'https://restaurant-template-demo.vercel.app',
  'https://restaurant-template-demo.vercel.app',
  'restaurant-food-delivery',
  ARRAY['Online Ordering', 'Menu Showcase', 'Table Reservations', 'Food Gallery', 'Chef Profiles', 'Customer Reviews'],
  ARRAY['Next.js 15', 'TypeScript', 'Tailwind CSS', 'Supabase', 'Payment Integration'],
  'intermediate',
  '3-4 hours',
  'standard',
  true,
  false,
  134,
  650,
  4.6
),
(
  'SaaS Dashboard Template',
  'Professional dashboard template for SaaS applications',
  'Comprehensive SaaS dashboard with analytics, user management, billing, and settings. Includes charts, tables, forms, and responsive design. Perfect for building admin panels and user dashboards.',
  2499,
  3299,
  24,
  'Technology',
  'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop',
  'https://saas-dashboard-demo.vercel.app',
  'https://saas-dashboard-demo.vercel.app',
  'saas-dashboard-template',
  ARRAY['Analytics Dashboard', 'User Management', 'Billing Integration', 'Charts & Graphs', 'Responsive Tables', 'Settings Panel'],
  ARRAY['Next.js 15', 'TypeScript', 'Tailwind CSS', 'Chart.js', 'Supabase', 'Stripe'],
  'advanced',
  '5-7 hours',
  'extended',
  true,
  true,
  98,
  480,
  4.8
),
(
  'Free Blog Template',
  'Clean and minimal blog template for writers and content creators',
  'Beautiful blog template with clean typography, reading progress, social sharing, and comment system. Perfect for personal blogs, news sites, and content creators.',
  0,
  NULL,
  0,
  'Blog',
  'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&h=600&fit=crop',
  'https://blog-template-demo.vercel.app',
  'https://blog-template-demo.vercel.app',
  'free-blog-template',
  ARRAY['Clean Typography', 'Reading Progress', 'Social Sharing', 'Comment System', 'SEO Optimized', 'Dark Mode'],
  ARRAY['Next.js 15', 'TypeScript', 'Tailwind CSS', 'MDX', 'Supabase'],
  'beginner',
  '1-2 hours',
  'standard',
  false,
  false,
  312,
  1580,
  4.5
) ON CONFLICT (title) DO NOTHING;

-- Update existing templates to have some sample data
UPDATE templates 
SET 
  is_featured = true,
  downloads_count = FLOOR(RANDOM() * 200) + 50,
  views_count = FLOOR(RANDOM() * 1000) + 200,
  rating = ROUND((RANDOM() * 1.5 + 3.5)::numeric, 1)
WHERE id IN (
  SELECT id FROM templates 
  ORDER BY created_at DESC 
  LIMIT 3
);

-- Create a function to automatically generate slug from title
CREATE OR REPLACE FUNCTION generate_slug_from_title()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.slug IS NULL OR NEW.slug = '' THEN
    NEW.slug := LOWER(REGEXP_REPLACE(NEW.title, '[^a-zA-Z0-9]+', '-', 'g'));
    NEW.slug := TRIM(BOTH '-' FROM NEW.slug);
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-generate slug
DROP TRIGGER IF EXISTS generate_slug_trigger ON templates;
CREATE TRIGGER generate_slug_trigger
  BEFORE INSERT OR UPDATE ON templates
  FOR EACH ROW
  EXECUTE FUNCTION generate_slug_from_title();

-- Create a function to calculate discount percentage automatically
CREATE OR REPLACE FUNCTION calculate_discount_percentage()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.original_price IS NOT NULL AND NEW.original_price > 0 AND NEW.price < NEW.original_price THEN
    NEW.discount_percentage := ROUND(((NEW.original_price - NEW.price)::numeric / NEW.original_price::numeric) * 100);
  ELSE
    NEW.discount_percentage := 0;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-calculate discount
DROP TRIGGER IF EXISTS calculate_discount_trigger ON templates;
CREATE TRIGGER calculate_discount_trigger
  BEFORE INSERT OR UPDATE ON templates
  FOR EACH ROW
  EXECUTE FUNCTION calculate_discount_percentage();
