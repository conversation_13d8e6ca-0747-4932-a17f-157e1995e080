(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[162],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(5155);r(2115);var a=r(9708),i=r(2085),n=r(9434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:r,size:i,asChild:d=!1,...o}=e,c=d?a.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,n.cn)(l({variant:r,size:i,className:t})),...o})}},968:(e,t,r)=>{"use strict";r.d(t,{b:()=>l});var s=r(2115),a=r(3655),i=r(5155),n=s.forwardRef((e,t)=>(0,i.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var l=n},2486:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},2523:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(5155);r(2115);var a=r(9434);function i(e){let{className:t,type:r,...i}=e;return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},2643:(e,t,r)=>{"use strict";r.d(t,{U:()=>a});var s=r(1935);function a(){let e="https://aovrwjzhqrgbdhszdowg.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFvdnJ3anpocXJnYmRoc3pkb3dnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4NTI5MjEsImV4cCI6MjA2NDQyODkyMX0.9TWCiuDturnqdOSlDeOWVroegkTM7Nra-W2LUoyGDSs";if(!e||!t)throw Error("Missing Supabase environment variables. Please check your .env.local file.");return(0,s.createBrowserClient)(e,t)}},3655:(e,t,r)=>{"use strict";r.d(t,{hO:()=>d,sG:()=>l});var s=r(2115),a=r(7650),i=r(9708),n=r(5155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.TL)(`Primitive.${t}`),a=s.forwardRef((e,s)=>{let{asChild:a,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(a?r:t,{...i,ref:s})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function d(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},4070:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var s=r(5155),a=r(2115),i=r(6874),n=r.n(i),l=r(6695),d=r(285),o=r(2523),c=r(5057),u=r(2643),m=r(6671),x=r(8883);let h=(0,r(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var f=r(2486);function v(){let[e,t]=(0,a.useState)(""),[r,i]=(0,a.useState)(!1),[v,p]=(0,a.useState)(!1),g=(0,u.U)(),b=async t=>{t.preventDefault(),i(!0);try{let{error:t}=await g.auth.resetPasswordForEmail(e,{redirectTo:"".concat(window.location.origin,"/reset-password")});if(t)throw t;p(!0),m.oR.success("Password reset email sent! Check your inbox.")}catch(e){console.error("Password reset error:",e),m.oR.error(e.message||"Failed to send reset email")}finally{i(!1)}};return v?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4",children:(0,s.jsxs)(l.Zp,{className:"w-full max-w-md shadow-xl",children:[(0,s.jsxs)(l.aR,{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(x.A,{className:"h-8 w-8 text-green-600"})}),(0,s.jsx)(l.ZB,{className:"text-2xl font-bold",children:"Check Your Email"}),(0,s.jsxs)(l.BT,{children:["We've sent a password reset link to ",e]})]}),(0,s.jsxs)(l.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"text-center text-sm text-muted-foreground",children:[(0,s.jsx)("p",{children:"Didn't receive the email? Check your spam folder or"}),(0,s.jsx)("button",{onClick:()=>p(!1),className:"text-blue-600 hover:text-blue-800 font-medium",children:"try again"})]}),(0,s.jsx)(n(),{href:"/login",children:(0,s.jsxs)(d.$,{variant:"outline",className:"w-full",children:[(0,s.jsx)(h,{className:"h-4 w-4 mr-2"}),"Back to Login"]})})]})]})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4",children:(0,s.jsxs)(l.Zp,{className:"w-full max-w-md shadow-xl",children:[(0,s.jsxs)(l.aR,{className:"text-center",children:[(0,s.jsx)(l.ZB,{className:"text-2xl font-bold",children:"Forgot Password"}),(0,s.jsx)(l.BT,{children:"Enter your email address and we'll send you a link to reset your password"})]}),(0,s.jsxs)(l.Wu,{children:[(0,s.jsxs)("form",{onSubmit:b,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"email",children:"Email Address"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(o.p,{id:"email",type:"email",placeholder:"Enter your email",value:e,onChange:e=>t(e.target.value),required:!0,className:"pl-10"}),(0,s.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"})]})]}),(0,s.jsx)(d.$,{type:"submit",className:"w-full",disabled:r,children:r?(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Sending..."]}):(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(f.A,{className:"h-4 w-4"}),"Send Reset Link"]})})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)(n(),{href:"/login",className:"text-sm text-blue-600 hover:text-blue-800 flex items-center justify-center gap-2",children:[(0,s.jsx)(h,{className:"h-4 w-4"}),"Back to Login"]})})]})]})})}},5057:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var s=r(5155);r(2115);var a=r(968),i=r(9434);function n(e){let{className:t,...r}=e;return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>o,ZB:()=>l,Zp:()=>i,aR:()=>n});var s=r(5155);r(2115);var a=r(9434);function i(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function n(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function l(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...r})}function d(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...r})}function o(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...r})}},8452:(e,t,r)=>{Promise.resolve().then(r.bind(r,4070))},8883:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var s=r(2596),a=r(9688);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[671,935,455,874,441,684,358],()=>t(8452)),_N_E=e.O()}]);