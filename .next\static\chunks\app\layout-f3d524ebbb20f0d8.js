(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{285:(e,t,s)=>{"use strict";s.d(t,{$:()=>l});var a=s(5155);s(2115);var r=s(9708),n=s(2085),i=s(9434);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:s,size:n,asChild:l=!1,...d}=e,c=l?r.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:s,size:n,className:t})),...d})}},347:()=>{},2643:(e,t,s)=>{"use strict";s.d(t,{U:()=>r});var a=s(1935);function r(){let e="https://aovrwjzhqrgbdhszdowg.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFvdnJ3anpocXJnYmRoc3pkb3dnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4NTI5MjEsImV4cCI6MjA2NDQyODkyMX0.9TWCiuDturnqdOSlDeOWVroegkTM7Nra-W2LUoyGDSs";if(!e||!t)throw Error("Missing Supabase environment variables. Please check your .env.local file.");return(0,a.createBrowserClient)(e,t)}},5955:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2093,23)),Promise.resolve().then(s.t.bind(s,7735,23)),Promise.resolve().then(s.t.bind(s,347,23)),Promise.resolve().then(s.bind(s,6284))},6126:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var a=s(5155);s(2115);var r=s(9708),n=s(2085),i=s(9434);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:s,asChild:n=!1,...l}=e,d=n?r.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(o({variant:s}),t),...l})}},6284:(e,t,s)=>{"use strict";s.d(t,{MainLayout:()=>$});var a=s(5155),r=s(2115),n=s(6874),i=s.n(n),o=s(5695),l=s(9434),d=s(285),c=s(6126),u=s(7340),m=s(7434),h=s(3127),f=s(8883),g=s(3783),v=s(5525),x=s(1007),p=s(381),b=s(4835),y=s(306),w=s(2643),j=s(6671);let N=[{name:"Home",href:"/",icon:u.A},{name:"Templates",href:"/templates",icon:m.A},{name:"Customize",href:"/customize",icon:h.A},{name:"Contact",href:"/contact",icon:f.A}];function k(e){var t;let s,{className:n}=e,u=(0,o.usePathname)(),m=(0,o.useRouter)(),h=(0,w.U)(),[f,k]=(0,r.useState)(null),[S,A]=(0,r.useState)(null),[I,E]=(0,r.useState)(!0);(0,r.useEffect)(()=>{O();let{data:{subscription:e}}=h.auth.onAuthStateChange(async(e,t)=>{"SIGNED_IN"===e&&(null==t?void 0:t.user)?await C(t.user):"SIGNED_OUT"===e&&(k(null),A(null))});return()=>e.unsubscribe()},[]);let O=async()=>{try{let{data:{user:e},error:t}=await h.auth.getUser();if(t)throw t;e&&(k(e),await C(e))}catch(e){console.error("Error checking user:",e)}finally{E(!1)}},C=async e=>{try{let{data:t,error:s}=await h.from("profiles").select("*").eq("id",e.id).single();if(s&&"PGRST116"!==s.code)throw s;A(t)}catch(e){console.error("Error loading profile:",e)}},z=async()=>{try{let{error:e}=await h.auth.signOut();if(e)throw e;j.oR.success("Signed out successfully"),m.push("/")}catch(e){console.error("Sign out error:",e),j.oR.error("Failed to sign out")}};return(0,a.jsxs)("div",{className:(0,l.cn)("flex flex-col h-full",n),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between px-6 py-4 border-b",children:[(0,a.jsx)("h1",{className:"text-xl font-bold",children:"KaleidoneX"}),(null==S?void 0:S.role)==="admin"&&(0,a.jsxs)(c.E,{variant:"secondary",className:"text-xs",children:[(0,a.jsx)(v.A,{className:"h-3 w-3 mr-1"}),"Admin"]})]}),(0,a.jsx)("nav",{className:"flex-1 px-4 py-6 space-y-2",children:(s=[...N],f&&(s.push({name:"Dashboard",href:"/dashboard",icon:g.A}),(null==S?void 0:S.role)==="admin"&&s.push({name:"Admin Panel",href:"/admin",icon:v.A})),s).map(e=>{let t=u===e.href;return(0,a.jsx)(i(),{href:e.href,children:(0,a.jsxs)(d.$,{variant:t?"secondary":"ghost",className:(0,l.cn)("w-full justify-start gap-3",t&&"bg-secondary"),children:[(0,a.jsx)(e.icon,{className:"h-4 w-4"}),e.name,"Admin Panel"===e.name&&(0,a.jsx)(c.E,{variant:"outline",className:"ml-auto text-xs",children:"Admin"})]})},e.name)})}),(0,a.jsx)("div",{className:"px-4 py-4 border-t",children:I?(0,a.jsxs)("div",{className:"flex items-center gap-3 px-3 py-2",children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-muted border-t-primary rounded-full animate-spin"}),(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Loading..."})]}):f?(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 px-3 py-2 rounded-md bg-muted/50",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center",children:(0,a.jsx)(x.A,{className:"h-4 w-4"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium truncate",children:(null==S?void 0:S.full_name)||(null==(t=f.email)?void 0:t.split("@")[0])||"User"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground truncate",children:f.email})]})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(i(),{href:"/dashboard",children:(0,a.jsxs)(d.$,{variant:"ghost",size:"sm",className:"w-full justify-start gap-3",children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),"Settings"]})}),(0,a.jsxs)(d.$,{variant:"ghost",size:"sm",className:"w-full justify-start gap-3 text-red-600 hover:text-red-700 hover:bg-red-50",onClick:z,children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),"Sign Out"]})]})]}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i(),{href:"/login",children:(0,a.jsxs)(d.$,{className:"w-full justify-start gap-3",children:[(0,a.jsx)(y.A,{className:"h-4 w-4"}),"Sign In"]})}),(0,a.jsx)(i(),{href:"/login",children:(0,a.jsxs)(d.$,{variant:"outline",className:"w-full justify-start gap-3",children:[(0,a.jsx)(x.A,{className:"h-4 w-4"}),"Sign Up"]})})]})})]})}var S=s(5452),A=s(4416);function I(e){let{...t}=e;return(0,a.jsx)(S.bL,{"data-slot":"sheet",...t})}function E(e){let{...t}=e;return(0,a.jsx)(S.l9,{"data-slot":"sheet-trigger",...t})}function O(e){let{...t}=e;return(0,a.jsx)(S.ZL,{"data-slot":"sheet-portal",...t})}function C(e){let{className:t,...s}=e;return(0,a.jsx)(S.hJ,{"data-slot":"sheet-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...s})}function z(e){let{className:t,children:s,side:r="right",...n}=e;return(0,a.jsxs)(O,{children:[(0,a.jsx)(C,{}),(0,a.jsxs)(S.UC,{"data-slot":"sheet-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===r&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===r&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===r&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===r&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t),...n,children:[s,(0,a.jsxs)(S.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,a.jsx)(A.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}var _=s(4783);let T=()=>{let e=sessionStorage.getItem("visitor_session_id");return e||(e="session_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),sessionStorage.setItem("visitor_session_id",e)),e},D=()=>{let e=navigator.userAgent.toLowerCase();return/tablet|ipad|playbook|silk/.test(e)?"tablet":/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/.test(e)?"mobile":"desktop"},L=()=>{let e=navigator.userAgent;return e.includes("Chrome")?"Chrome":e.includes("Firefox")?"Firefox":e.includes("Safari")?"Safari":e.includes("Edge")?"Edge":e.includes("Opera")?"Opera":"Other"},U=()=>{let e=navigator.userAgent;return e.includes("Windows")?"Windows":e.includes("Mac")?"macOS":e.includes("Linux")?"Linux":e.includes("Android")?"Android":e.includes("iOS")?"iOS":"Other"},P=()=>{let e=navigator.connection||navigator.mozConnection||navigator.webkitConnection;return(null==e?void 0:e.effectiveType)||"unknown"},F=()=>{try{var e;let t=Intl.DateTimeFormat().resolvedOptions().timeZone,s={"America/New_York":"United States","America/Los_Angeles":"United States","Europe/London":"United Kingdom","Europe/Paris":"France","Europe/Berlin":"Germany","Asia/Tokyo":"Japan","Asia/Shanghai":"China","Asia/Kolkata":"India","Australia/Sydney":"Australia"}[t]||"Unknown",a=(null==(e=t.split("/")[1])?void 0:e.replace("_"," "))||"Unknown";return{country:s,city:a}}catch(e){return{country:"unknown",city:"unknown"}}};var J=s(1362);let M=e=>{let{...t}=e,{theme:s="system"}=(0,J.D)();return(0,a.jsx)(j.l$,{theme:s,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...t})};function $(e){let{children:t}=e,[s,n]=(0,r.useState)(!1),i=function(){let[e,t]=r.useState(void 0);return r.useEffect(()=>{let e=window.matchMedia("(max-width: ".concat(767,"px)")),s=()=>{t(window.innerWidth<768)};return e.addEventListener("change",s),t(window.innerWidth<768),()=>e.removeEventListener("change",s)},[]),!!e}(),l=(0,o.usePathname)(),[c,u]=(0,r.useState)(null);return(0,r.useEffect)(()=>{let e=performance.now(),t=()=>{u(Math.round(performance.now()-e))};if("complete"!==document.readyState)return window.addEventListener("load",t),()=>window.removeEventListener("load",t);t()},[]),(0,r.useEffect)(()=>{let e=setTimeout(async()=>{try{let{country:e,city:t}=F(),s={path:l,userAgent:navigator.userAgent,referrer:document.referrer,screenResolution:"".concat(screen.width,"x").concat(screen.height),language:navigator.language,timezone:Intl.DateTimeFormat().resolvedOptions().timeZone,sessionId:T(),deviceType:D(),browser:L(),os:U(),country:e,city:t,pageTitle:document.title,loadTime:c,connectionType:P(),timestamp:new Date().toISOString()};await fetch("/api/track-visitor",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)})}catch(e){console.warn("Failed to track visitor:",e)}},1e3);return()=>clearTimeout(e)},[l,c]),(0,a.jsxs)("div",{className:"flex h-screen bg-background",children:[!i&&(0,a.jsx)("div",{className:"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0",children:(0,a.jsx)("div",{className:"flex flex-col flex-grow border-r bg-card",children:(0,a.jsx)(k,{})})}),i&&(0,a.jsx)(I,{open:s,onOpenChange:n,children:(0,a.jsx)(z,{side:"left",className:"p-0 w-64",children:(0,a.jsx)(k,{})})}),(0,a.jsxs)("div",{className:"flex flex-col flex-1 ".concat(i?"":"md:pl-64"),children:[(0,a.jsxs)("header",{className:"flex items-center justify-between px-6 py-4 bg-card border-b",children:[i&&(0,a.jsx)(I,{open:s,onOpenChange:n,children:(0,a.jsx)(E,{asChild:!0,children:(0,a.jsx)(d.$,{variant:"ghost",size:"icon",children:(0,a.jsx)(_.A,{className:"h-5 w-5"})})})}),(0,a.jsx)("div",{className:"flex items-center space-x-4 ml-auto",children:(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Welcome back!"})})]}),(0,a.jsx)("main",{className:"flex-1 overflow-auto p-6",children:t})]}),(0,a.jsx)(M,{})]})}},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var a=s(2596),r=s(9688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[360,671,935,455,874,688,519,441,684,358],()=>t(5955)),_N_E=e.O()}]);