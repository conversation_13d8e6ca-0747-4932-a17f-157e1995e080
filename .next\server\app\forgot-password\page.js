(()=>{var e={};e.id=162,e.ids=[162],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27900:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36200:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\kaleidonex\\\\src\\\\app\\\\forgot-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\forgot-password\\page.tsx","default")},40715:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(60687),a=r(43210),n=r(85814),i=r.n(n),o=r(44493),l=r(29523),d=r(89667),c=r(54300),u=r(79481),p=r(52581),x=r(41550);let m=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var f=r(27900);function h(){let[e,t]=(0,a.useState)(""),[r,n]=(0,a.useState)(!1),[h,g]=(0,a.useState)(!1),v=(0,u.U)(),b=async t=>{t.preventDefault(),n(!0);try{let{error:t}=await v.auth.resetPasswordForEmail(e,{redirectTo:`${window.location.origin}/reset-password`});if(t)throw t;g(!0),p.oR.success("Password reset email sent! Check your inbox.")}catch(e){console.error("Password reset error:",e),p.oR.error(e.message||"Failed to send reset email")}finally{n(!1)}};return h?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4",children:(0,s.jsxs)(o.Zp,{className:"w-full max-w-md shadow-xl",children:[(0,s.jsxs)(o.aR,{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(x.A,{className:"h-8 w-8 text-green-600"})}),(0,s.jsx)(o.ZB,{className:"text-2xl font-bold",children:"Check Your Email"}),(0,s.jsxs)(o.BT,{children:["We've sent a password reset link to ",e]})]}),(0,s.jsxs)(o.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"text-center text-sm text-muted-foreground",children:[(0,s.jsx)("p",{children:"Didn't receive the email? Check your spam folder or"}),(0,s.jsx)("button",{onClick:()=>g(!1),className:"text-blue-600 hover:text-blue-800 font-medium",children:"try again"})]}),(0,s.jsx)(i(),{href:"/login",children:(0,s.jsxs)(l.$,{variant:"outline",className:"w-full",children:[(0,s.jsx)(m,{className:"h-4 w-4 mr-2"}),"Back to Login"]})})]})]})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4",children:(0,s.jsxs)(o.Zp,{className:"w-full max-w-md shadow-xl",children:[(0,s.jsxs)(o.aR,{className:"text-center",children:[(0,s.jsx)(o.ZB,{className:"text-2xl font-bold",children:"Forgot Password"}),(0,s.jsx)(o.BT,{children:"Enter your email address and we'll send you a link to reset your password"})]}),(0,s.jsxs)(o.Wu,{children:[(0,s.jsxs)("form",{onSubmit:b,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"email",children:"Email Address"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(d.p,{id:"email",type:"email",placeholder:"Enter your email",value:e,onChange:e=>t(e.target.value),required:!0,className:"pl-10"}),(0,s.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"})]})]}),(0,s.jsx)(l.$,{type:"submit",className:"w-full",disabled:r,children:r?(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Sending..."]}):(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(f.A,{className:"h-4 w-4"}),"Send Reset Link"]})})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)(i(),{href:"/login",className:"text-sm text-blue-600 hover:text-blue-800 flex items-center justify-center gap-2",children:[(0,s.jsx)(m,{className:"h-4 w-4"}),"Back to Login"]})})]})]})})}},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>i});var s=r(60687);r(43210);var a=r(4780);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},51116:(e,t,r)=>{Promise.resolve().then(r.bind(r,40715))},54300:(e,t,r)=>{"use strict";r.d(t,{J:()=>l});var s=r(60687),a=r(43210),n=r(14163),i=a.forwardRef((e,t)=>(0,s.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=r(4780);function l({className:e,...t}){return(0,s.jsx)(i,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},78644:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,36200)),"C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\forgot-password\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\forgot-password\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/forgot-password/page",pathname:"/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87564:(e,t,r)=>{Promise.resolve().then(r.bind(r,36200))},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(60687);r(43210);var a=r(4780);function n({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,529,658,391],()=>r(78644));module.exports=s})();