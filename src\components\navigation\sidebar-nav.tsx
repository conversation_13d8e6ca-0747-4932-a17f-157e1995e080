"use client"

import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Home,
  FileText,
  Palette,
  Mail,
  LogOut,
  LogIn,
  User,
  Settings,
  Shield,
  LayoutDashboard
} from "lucide-react"
import { useState, useEffect } from "react"
import { createClient } from "@/lib/supabase/client"
import { toast } from "sonner"

const navigation = [
  {
    name: "Home",
    href: "/",
    icon: Home,
  },
  {
    name: "Templates",
    href: "/templates",
    icon: FileText,
  },
  {
    name: "Customize",
    href: "/customize",
    icon: Palette,
  },
  {
    name: "Contact",
    href: "/contact",
    icon: Mail,
  },
]

interface SidebarNavProps {
  className?: string
}

export function SidebarNav({ className }: SidebarNavProps) {
  const pathname = usePathname()
  const router = useRouter()
  const supabase = createClient()

  const [user, setUser] = useState<any>(null)
  const [profile, setProfile] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    checkUser()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          await loadUserProfile(session.user)
        } else if (event === 'SIGNED_OUT') {
          setUser(null)
          setProfile(null)
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const checkUser = async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()

      if (error) throw error

      if (user) {
        setUser(user)
        await loadUserProfile(user)
      }
    } catch (error) {
      console.error('Error checking user:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadUserProfile = async (user: any) => {
    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (error && error.code !== 'PGRST116') {
        throw error
      }

      setProfile(profile)
    } catch (error) {
      console.error('Error loading profile:', error)
    }
  }

  const handleSignOut = async () => {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error

      toast.success('Signed out successfully')
      router.push('/')
    } catch (error: any) {
      console.error('Sign out error:', error)
      toast.error('Failed to sign out')
    }
  }

  // Filter navigation based on user role
  const getFilteredNavigation = () => {
    let nav = [...navigation]

    if (user) {
      // Add authenticated user navigation
      nav.push({
        name: "Dashboard",
        href: "/dashboard",
        icon: LayoutDashboard,
      })

      // Add admin navigation if user is admin
      if (profile?.role === 'admin') {
        nav.push({
          name: "Admin Panel",
          href: "/admin",
          icon: Shield,
        })
      }
    }

    return nav
  }

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Logo */}
      <div className="flex items-center justify-between px-6 py-4 border-b">
        <h1 className="text-xl font-bold">KaleidoneX</h1>
        {profile?.role === 'admin' && (
          <Badge variant="secondary" className="text-xs">
            <Shield className="h-3 w-3 mr-1" />
            Admin
          </Badge>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-2">
        {getFilteredNavigation().map((item) => {
          const isActive = pathname === item.href
          return (
            <Link key={item.name} href={item.href}>
              <Button
                variant={isActive ? "secondary" : "ghost"}
                className={cn(
                  "w-full justify-start gap-3",
                  isActive && "bg-secondary"
                )}
              >
                <item.icon className="h-4 w-4" />
                {item.name}
                {item.name === "Admin Panel" && (
                  <Badge variant="outline" className="ml-auto text-xs">
                    Admin
                  </Badge>
                )}
              </Button>
            </Link>
          )
        })}
      </nav>

      {/* User section */}
      <div className="px-4 py-4 border-t">
        {loading ? (
          <div className="flex items-center gap-3 px-3 py-2">
            <div className="w-4 h-4 border-2 border-muted border-t-primary rounded-full animate-spin" />
            <span className="text-sm text-muted-foreground">Loading...</span>
          </div>
        ) : user ? (
          <div className="space-y-2">
            {/* User Info */}
            <div className="flex items-center gap-3 px-3 py-2 rounded-md bg-muted/50">
              <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                <User className="h-4 w-4" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">
                  {profile?.full_name || user.email?.split('@')[0] || 'User'}
                </p>
                <p className="text-xs text-muted-foreground truncate">
                  {user.email}
                </p>
              </div>
            </div>

            {/* User Actions */}
            <div className="space-y-1">
              <Link href="/dashboard">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start gap-3"
                >
                  <Settings className="h-4 w-4" />
                  Settings
                </Button>
              </Link>

              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start gap-3 text-red-600 hover:text-red-700 hover:bg-red-50"
                onClick={handleSignOut}
              >
                <LogOut className="h-4 w-4" />
                Sign Out
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            <Link href="/login">
              <Button className="w-full justify-start gap-3">
                <LogIn className="h-4 w-4" />
                Sign In
              </Button>
            </Link>

            <Link href="/login">
              <Button variant="outline" className="w-full justify-start gap-3">
                <User className="h-4 w-4" />
                Sign Up
              </Button>
            </Link>
          </div>
        )}
      </div>
    </div>
  )
}
