{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  const url = process.env.NEXT_PUBLIC_SUPABASE_URL\n  const key = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n\n  if (!url || !key) {\n    throw new Error('Missing Supabase environment variables. Please check your .env.local file.')\n  }\n\n  return createBrowserClient(url, key)\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,SAAS;IACd,MAAM;IACN,MAAM;IAEN,uCAAkB;;IAElB;IAEA,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK;AAClC", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/components/navigation/sidebar-nav.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { usePathname, useRouter } from \"next/navigation\"\nimport { cn } from \"@/lib/utils\"\nimport { Button } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport {\n  Home,\n  FileText,\n  Palette,\n  Mail,\n  LogOut,\n  LogIn,\n  User,\n  Settings,\n  Shield,\n  LayoutDashboard\n} from \"lucide-react\"\nimport { useState, useEffect } from \"react\"\nimport { createClient } from \"@/lib/supabase/client\"\nimport { toast } from \"sonner\"\n\nconst navigation = [\n  {\n    name: \"Home\",\n    href: \"/\",\n    icon: Home,\n  },\n  {\n    name: \"Templates\",\n    href: \"/templates\",\n    icon: FileText,\n  },\n  {\n    name: \"Customize\",\n    href: \"/customize\",\n    icon: Palette,\n  },\n  {\n    name: \"Contact\",\n    href: \"/contact\",\n    icon: Mail,\n  },\n]\n\ninterface SidebarNavProps {\n  className?: string\n}\n\nexport function SidebarNav({ className }: SidebarNavProps) {\n  const pathname = usePathname()\n  const router = useRouter()\n  const supabase = createClient()\n\n  const [user, setUser] = useState<any>(null)\n  const [profile, setProfile] = useState<any>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    checkUser()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        if (event === 'SIGNED_IN' && session?.user) {\n          await loadUserProfile(session.user)\n        } else if (event === 'SIGNED_OUT') {\n          setUser(null)\n          setProfile(null)\n        }\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const checkUser = async () => {\n    try {\n      const { data: { user }, error } = await supabase.auth.getUser()\n\n      if (error) throw error\n\n      if (user) {\n        setUser(user)\n        await loadUserProfile(user)\n      }\n    } catch (error) {\n      console.error('Error checking user:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const loadUserProfile = async (user: any) => {\n    try {\n      const { data: profile, error } = await supabase\n        .from('profiles')\n        .select('*')\n        .eq('id', user.id)\n        .single()\n\n      if (error && error.code !== 'PGRST116') {\n        throw error\n      }\n\n      setProfile(profile)\n    } catch (error) {\n      console.error('Error loading profile:', error)\n    }\n  }\n\n  const handleSignOut = async () => {\n    try {\n      const { error } = await supabase.auth.signOut()\n      if (error) throw error\n\n      toast.success('Signed out successfully')\n      router.push('/')\n    } catch (error: any) {\n      console.error('Sign out error:', error)\n      toast.error('Failed to sign out')\n    }\n  }\n\n  // Filter navigation based on user role\n  const getFilteredNavigation = () => {\n    let nav = [...navigation]\n\n    if (user) {\n      // Add authenticated user navigation\n      nav.push({\n        name: \"Dashboard\",\n        href: \"/dashboard\",\n        icon: LayoutDashboard,\n      })\n\n      // Add admin navigation if user is admin\n      if (profile?.role === 'admin') {\n        nav.push({\n          name: \"Admin Panel\",\n          href: \"/admin\",\n          icon: Shield,\n        })\n      }\n    }\n\n    return nav\n  }\n\n  return (\n    <div className={cn(\"flex flex-col h-full\", className)}>\n      {/* Logo */}\n      <div className=\"flex items-center justify-between px-6 py-4 border-b\">\n        <h1 className=\"text-xl font-bold\">KaleidoneX</h1>\n        {profile?.role === 'admin' && (\n          <Badge variant=\"secondary\" className=\"text-xs\">\n            <Shield className=\"h-3 w-3 mr-1\" />\n            Admin\n          </Badge>\n        )}\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 px-4 py-6 space-y-2\">\n        {getFilteredNavigation().map((item) => {\n          const isActive = pathname === item.href\n          return (\n            <Link key={item.name} href={item.href}>\n              <Button\n                variant={isActive ? \"secondary\" : \"ghost\"}\n                className={cn(\n                  \"w-full justify-start gap-3\",\n                  isActive && \"bg-secondary\"\n                )}\n              >\n                <item.icon className=\"h-4 w-4\" />\n                {item.name}\n                {item.name === \"Admin Panel\" && (\n                  <Badge variant=\"outline\" className=\"ml-auto text-xs\">\n                    Admin\n                  </Badge>\n                )}\n              </Button>\n            </Link>\n          )\n        })}\n      </nav>\n\n      {/* User section */}\n      <div className=\"px-4 py-4 border-t\">\n        {loading ? (\n          <div className=\"flex items-center gap-3 px-3 py-2\">\n            <div className=\"w-4 h-4 border-2 border-muted border-t-primary rounded-full animate-spin\" />\n            <span className=\"text-sm text-muted-foreground\">Loading...</span>\n          </div>\n        ) : user ? (\n          <div className=\"space-y-2\">\n            {/* User Info */}\n            <div className=\"flex items-center gap-3 px-3 py-2 rounded-md bg-muted/50\">\n              <div className=\"w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center\">\n                <User className=\"h-4 w-4\" />\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium truncate\">\n                  {profile?.full_name || user.email?.split('@')[0] || 'User'}\n                </p>\n                <p className=\"text-xs text-muted-foreground truncate\">\n                  {user.email}\n                </p>\n              </div>\n            </div>\n\n            {/* User Actions */}\n            <div className=\"space-y-1\">\n              <Link href=\"/dashboard\">\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className=\"w-full justify-start gap-3\"\n                >\n                  <Settings className=\"h-4 w-4\" />\n                  Settings\n                </Button>\n              </Link>\n\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"w-full justify-start gap-3 text-red-600 hover:text-red-700 hover:bg-red-50\"\n                onClick={handleSignOut}\n              >\n                <LogOut className=\"h-4 w-4\" />\n                Sign Out\n              </Button>\n            </div>\n          </div>\n        ) : (\n          <div className=\"space-y-2\">\n            <Link href=\"/login\">\n              <Button className=\"w-full justify-start gap-3\">\n                <LogIn className=\"h-4 w-4\" />\n                Sign In\n              </Button>\n            </Link>\n\n            <Link href=\"/login\">\n              <Button variant=\"outline\" className=\"w-full justify-start gap-3\">\n                <User className=\"h-4 w-4\" />\n                Sign Up\n              </Button>\n            </Link>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AArBA;;;;;;;;;;;AAuBA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,mMAAA,CAAA,OAAI;IACZ;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,8MAAA,CAAA,WAAQ;IAChB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,wMAAA,CAAA,UAAO;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kMAAA,CAAA,OAAI;IACZ;CACD;AAMM,SAAS,WAAW,EAAE,SAAS,EAAmB;IACvD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC5C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,IAAI,UAAU,eAAe,SAAS,MAAM;gBAC1C,MAAM,gBAAgB,QAAQ,IAAI;YACpC,OAAO,IAAI,UAAU,cAAc;gBACjC,QAAQ;gBACR,WAAW;YACb;QACF;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YAE7D,IAAI,OAAO,MAAM;YAEjB,IAAI,MAAM;gBACR,QAAQ;gBACR,MAAM,gBAAgB;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;YAET,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY;gBACtC,MAAM;YACR;YAEA,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YAC7C,IAAI,OAAO,MAAM;YAEjB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,mBAAmB;YACjC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,uCAAuC;IACvC,MAAM,wBAAwB;QAC5B,IAAI,MAAM;eAAI;SAAW;QAEzB,IAAI,MAAM;YACR,oCAAoC;YACpC,IAAI,IAAI,CAAC;gBACP,MAAM;gBACN,MAAM;gBACN,MAAM,4NAAA,CAAA,kBAAe;YACvB;YAEA,wCAAwC;YACxC,IAAI,SAAS,SAAS,SAAS;gBAC7B,IAAI,IAAI,CAAC;oBACP,MAAM;oBACN,MAAM;oBACN,MAAM,sMAAA,CAAA,SAAM;gBACd;YACF;QACF;QAEA,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;;0BAEzC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoB;;;;;;oBACjC,SAAS,SAAS,yBACjB,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAY,WAAU;;0CACnC,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAOzC,8OAAC;gBAAI,WAAU;0BACZ,wBAAwB,GAAG,CAAC,CAAC;oBAC5B,MAAM,WAAW,aAAa,KAAK,IAAI;oBACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;wBAAiB,MAAM,KAAK,IAAI;kCACnC,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS,WAAW,cAAc;4BAClC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8BACA,YAAY;;8CAGd,8OAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;gCACpB,KAAK,IAAI;gCACT,KAAK,IAAI,KAAK,+BACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAAkB;;;;;;;;;;;;uBAXhD,KAAK,IAAI;;;;;gBAkBxB;;;;;;0BAIF,8OAAC;gBAAI,WAAU;0BACZ,wBACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAK,WAAU;sCAAgC;;;;;;;;;;;2BAEhD,qBACF,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDACV,SAAS,aAAa,KAAK,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI;;;;;;sDAEtD,8OAAC;4CAAE,WAAU;sDACV,KAAK,KAAK;;;;;;;;;;;;;;;;;;sCAMjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAKpC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;;sDAET,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;yCAMpC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,WAAU;;kDAChB,8OAAC,wMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAKjC,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,WAAU;;kDAClC,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5C", "debugId": null}}, {"offset": {"line": 654, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 826, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/hooks/use-mobile.ts"], "sourcesContent": ["import * as React from \"react\"\n\nconst MO<PERSON>LE_BREAKPOINT = 768\n\nexport function useIsMobile() {\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\n\n  React.useEffect(() => {\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\n    const onChange = () => {\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    }\n    mql.addEventListener(\"change\", onChange)\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    return () => mql.removeEventListener(\"change\", onChange)\n  }, [])\n\n  return !!isMobile\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QACvE,MAAM,WAAW;YACf,YAAY,OAAO,UAAU,GAAG;QAClC;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,YAAY,OAAO,UAAU,GAAG;QAChC,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 851, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/hooks/use-visitor-tracking.ts"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useState } from 'react'\nimport { usePathname } from 'next/navigation'\n\n// Generate a session ID that persists for the browser session\nconst getSessionId = () => {\n  if (typeof window === 'undefined') return 'server'\n\n  let sessionId = sessionStorage.getItem('visitor_session_id')\n  if (!sessionId) {\n    sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n    sessionStorage.setItem('visitor_session_id', sessionId)\n  }\n  return sessionId\n}\n\n// Detect device type\nconst getDeviceType = () => {\n  if (typeof window === 'undefined') return 'unknown'\n\n  const userAgent = navigator.userAgent.toLowerCase()\n  if (/tablet|ipad|playbook|silk/.test(userAgent)) return 'tablet'\n  if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\\sce|palm|smartphone|iemobile/.test(userAgent)) return 'mobile'\n  return 'desktop'\n}\n\n// Detect browser\nconst getBrowser = () => {\n  if (typeof window === 'undefined') return 'unknown'\n\n  const userAgent = navigator.userAgent\n  if (userAgent.includes('Chrome')) return 'Chrome'\n  if (userAgent.includes('Firefox')) return 'Firefox'\n  if (userAgent.includes('Safari')) return 'Safari'\n  if (userAgent.includes('Edge')) return 'Edge'\n  if (userAgent.includes('Opera')) return 'Opera'\n  return 'Other'\n}\n\n// Detect operating system\nconst getOperatingSystem = () => {\n  if (typeof window === 'undefined') return 'unknown'\n\n  const userAgent = navigator.userAgent\n  if (userAgent.includes('Windows')) return 'Windows'\n  if (userAgent.includes('Mac')) return 'macOS'\n  if (userAgent.includes('Linux')) return 'Linux'\n  if (userAgent.includes('Android')) return 'Android'\n  if (userAgent.includes('iOS')) return 'iOS'\n  return 'Other'\n}\n\n// Get connection type (if available)\nconst getConnectionType = () => {\n  if (typeof window === 'undefined') return 'unknown'\n\n  // @ts-ignore - navigator.connection is experimental\n  const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection\n  return connection?.effectiveType || 'unknown'\n}\n\n// Get approximate location (using timezone)\nconst getLocationInfo = () => {\n  if (typeof window === 'undefined') return { country: 'unknown', city: 'unknown' }\n\n  try {\n    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone\n    // Simple mapping of timezones to countries (basic implementation)\n    const timezoneToCountry: Record<string, string> = {\n      'America/New_York': 'United States',\n      'America/Los_Angeles': 'United States',\n      'Europe/London': 'United Kingdom',\n      'Europe/Paris': 'France',\n      'Europe/Berlin': 'Germany',\n      'Asia/Tokyo': 'Japan',\n      'Asia/Shanghai': 'China',\n      'Asia/Kolkata': 'India',\n      'Australia/Sydney': 'Australia',\n    }\n\n    const country = timezoneToCountry[timezone] || 'Unknown'\n    const city = timezone.split('/')[1]?.replace('_', ' ') || 'Unknown'\n\n    return { country, city }\n  } catch {\n    return { country: 'unknown', city: 'unknown' }\n  }\n}\n\nexport function useVisitorTracking() {\n  const pathname = usePathname()\n  const [pageLoadTime, setPageLoadTime] = useState<number | null>(null)\n\n  useEffect(() => {\n    // Measure page load time\n    const startTime = performance.now()\n\n    const handleLoad = () => {\n      const loadTime = Math.round(performance.now() - startTime)\n      setPageLoadTime(loadTime)\n    }\n\n    if (document.readyState === 'complete') {\n      handleLoad()\n    } else {\n      window.addEventListener('load', handleLoad)\n      return () => window.removeEventListener('load', handleLoad)\n    }\n  }, [])\n\n  useEffect(() => {\n    const trackVisitor = async () => {\n      try {\n        if (typeof window === 'undefined') return\n\n        const { country, city } = getLocationInfo()\n\n        const trackingData = {\n          path: pathname,\n          userAgent: navigator.userAgent,\n          referrer: document.referrer,\n          screenResolution: `${screen.width}x${screen.height}`,\n          language: navigator.language,\n          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,\n          sessionId: getSessionId(),\n          deviceType: getDeviceType(),\n          browser: getBrowser(),\n          os: getOperatingSystem(),\n          country,\n          city,\n          pageTitle: document.title,\n          loadTime: pageLoadTime,\n          connectionType: getConnectionType(),\n          timestamp: new Date().toISOString(),\n        }\n\n        await fetch('/api/track-visitor', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify(trackingData),\n        })\n      } catch (error) {\n        // Silently fail - visitor tracking shouldn't break the app\n        console.warn('Failed to track visitor:', error)\n      }\n    }\n\n    // Add a small delay to ensure page is fully loaded\n    const timer = setTimeout(trackVisitor, 1000)\n    return () => clearTimeout(timer)\n  }, [pathname, pageLoadTime])\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AAKA,8DAA8D;AAC9D,MAAM,eAAe;IACnB,wCAAmC,OAAO;;IAE1C,IAAI;AAMN;AAEA,qBAAqB;AACrB,MAAM,gBAAgB;IACpB,wCAAmC,OAAO;;IAE1C,MAAM;AAIR;AAEA,iBAAiB;AACjB,MAAM,aAAa;IACjB,wCAAmC,OAAO;;IAE1C,MAAM;AAOR;AAEA,0BAA0B;AAC1B,MAAM,qBAAqB;IACzB,wCAAmC,OAAO;;IAE1C,MAAM;AAOR;AAEA,qCAAqC;AACrC,MAAM,oBAAoB;IACxB,wCAAmC,OAAO;;IAE1C,oDAAoD;IACpD,MAAM;AAER;AAEA,4CAA4C;AAC5C,MAAM,kBAAkB;IACtB,wCAAmC,OAAO;QAAE,SAAS;QAAW,MAAM;IAAU;;AAwBlF;AAEO,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yBAAyB;QACzB,MAAM,YAAY,YAAY,GAAG;QAEjC,MAAM,aAAa;YACjB,MAAM,WAAW,KAAK,KAAK,CAAC,YAAY,GAAG,KAAK;YAChD,gBAAgB;QAClB;QAEA,IAAI,SAAS,UAAU,KAAK,YAAY;YACtC;QACF,OAAO;YACL,OAAO,gBAAgB,CAAC,QAAQ;YAChC,OAAO,IAAM,OAAO,mBAAmB,CAAC,QAAQ;QAClD;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI;gBACF,wCAAmC;;gBAEnC,MAAQ,qBAAS;gBAEjB,MAAM;YA0BR,EAAE,OAAO,OAAO;gBACd,2DAA2D;gBAC3D,QAAQ,IAAI,CAAC,4BAA4B;YAC3C;QACF;QAEA,mDAAmD;QACnD,MAAM,QAAQ,WAAW,cAAc;QACvC,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAU;KAAa;AAC7B", "debugId": null}}, {"offset": {"line": 941, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useTheme } from \"next-themes\"\nimport { Toaster as Son<PERSON>, ToasterP<PERSON> } from \"sonner\"\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme()\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n          \"--normal-border\": \"var(--border)\",\n        } as React.CSSProperties\n      }\n      {...props}\n    />\n  )\n}\n\nexport { Toaster }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,8OAAC,wIAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 975, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/components/layout/main-layout.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { SidebarNav } from \"@/components/navigation/sidebar-nav\"\nimport { Button } from \"@/components/ui/button\"\nimport { Sheet, SheetContent, SheetTrigger } from \"@/components/ui/sheet\"\nimport { Menu } from \"lucide-react\"\nimport { useIsMobile } from \"@/hooks/use-mobile\"\nimport { useVisitorTracking } from \"@/hooks/use-visitor-tracking\"\nimport { Toaster } from \"@/components/ui/sonner\"\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const isMobile = useIsMobile()\n\n  // Track visitor\n  useVisitorTracking()\n\n  return (\n    <div className=\"flex h-screen bg-background\">\n      {/* Desktop Sidebar */}\n      {!isMobile && (\n        <div className=\"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0\">\n          <div className=\"flex flex-col flex-grow border-r bg-card\">\n            <SidebarNav />\n          </div>\n        </div>\n      )}\n\n      {/* Mobile Sidebar */}\n      {isMobile && (\n        <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>\n          <SheetContent side=\"left\" className=\"p-0 w-64\">\n            <SidebarNav />\n          </SheetContent>\n        </Sheet>\n      )}\n\n      {/* Main Content */}\n      <div className={`flex flex-col flex-1 ${!isMobile ? 'md:pl-64' : ''}`}>\n        {/* Header */}\n        <header className=\"flex items-center justify-between px-6 py-4 bg-card border-b\">\n          {isMobile && (\n            <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>\n              <SheetTrigger asChild>\n                <Button variant=\"ghost\" size=\"icon\">\n                  <Menu className=\"h-5 w-5\" />\n                </Button>\n              </SheetTrigger>\n            </Sheet>\n          )}\n          \n          <div className=\"flex items-center space-x-4 ml-auto\">\n            <div className=\"text-sm text-muted-foreground\">\n              Welcome back!\n            </div>\n          </div>\n        </header>\n\n        {/* Page Content */}\n        <main className=\"flex-1 overflow-auto p-6\">\n          {children}\n        </main>\n      </div>\n\n      {/* Toast Notifications */}\n      <Toaster />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAeO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD;IAE3B,gBAAgB;IAChB,CAAA,GAAA,0IAAA,CAAA,qBAAkB,AAAD;IAEjB,qBACE,8OAAC;QAAI,WAAU;;YAEZ,CAAC,0BACA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kJAAA,CAAA,aAAU;;;;;;;;;;;;;;;YAMhB,0BACC,8OAAC,iIAAA,CAAA,QAAK;gBAAC,MAAM;gBAAa,cAAc;0BACtC,cAAA,8OAAC,iIAAA,CAAA,eAAY;oBAAC,MAAK;oBAAO,WAAU;8BAClC,cAAA,8OAAC,kJAAA,CAAA,aAAU;;;;;;;;;;;;;;;0BAMjB,8OAAC;gBAAI,WAAW,CAAC,qBAAqB,EAAE,CAAC,WAAW,aAAa,IAAI;;kCAEnE,8OAAC;wBAAO,WAAU;;4BACf,0BACC,8OAAC,iIAAA,CAAA,QAAK;gCAAC,MAAM;gCAAa,cAAc;0CACtC,cAAA,8OAAC,iIAAA,CAAA,eAAY;oCAAC,OAAO;8CACnB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;0CAMxB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CAAgC;;;;;;;;;;;;;;;;;kCAOnD,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;0BAKL,8OAAC,kIAAA,CAAA,UAAO;;;;;;;;;;;AAGd", "debugId": null}}]}