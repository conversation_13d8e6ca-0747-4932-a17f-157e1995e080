(()=>{var e={};e.id=737,e.ids=[737],e.modules={2507:(e,r,t)=>{"use strict";t.d(r,{U:()=>a});var s=t(34386),i=t(44999);async function a(){let e=await (0,i.UL)(),r="https://aovrwjzhqrgbdhszdowg.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFvdnJ3anpocXJnYmRoc3pkb3dnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4NTI5MjEsImV4cCI6MjA2NDQyODkyMX0.9TWCiuDturnqdOSlDeOWVroegkTM7Nra-W2LUoyGDSs";if(!r||!t)throw Error("Missing Supabase environment variables. Please check your .env.local file.");return(0,s.createServerClient)(r,t,{cookies:{getAll:()=>e.getAll(),setAll(r){try{r.forEach(({name:r,value:t,options:s})=>e.set(r,t,s))}catch{}}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94657:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>l,serverHooks:()=>x,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>y});var s={};t.r(s),t.d(s,{POST:()=>d});var i=t(96559),a=t(48088),o=t(37719),n=t(32190),u=t(55511),p=t.n(u),c=t(2507);async function d(e){try{let{razorpay_order_id:r,razorpay_payment_id:t,razorpay_signature:s,templateId:i}=await e.json(),a=r+"|"+t;if(p().createHmac("sha256",process.env.RAZORPAY_KEY_SECRET).update(a.toString()).digest("hex")!==s)return n.NextResponse.json({error:"Invalid signature"},{status:400});let o=await (0,c.U)(),{data:{user:u},error:d}=await o.auth.getUser();if(d||!u)return n.NextResponse.json({error:"User not authenticated"},{status:401});let{error:l}=await o.from("orders").update({status:"completed",razorpay_payment_id:t,razorpay_signature:s,updated_at:new Date().toISOString()}).eq("razorpay_order_id",r).eq("user_id",u.id);if(l)return console.error("Database update error:",l),n.NextResponse.json({error:"Failed to update order"},{status:500});if(i){let{data:e,error:s}=await o.from("orders").select("amount, currency").eq("razorpay_order_id",r).eq("user_id",u.id).single();if(!s&&e){let{error:r}=await o.from("purchases").insert({user_id:u.id,template_id:i,amount:e.amount,currency:e.currency,razorpay_payment_id:t,status:"completed"});r&&console.error("Purchase record creation error:",r)}}return n.NextResponse.json({success:!0,message:"Payment verified successfully"})}catch(e){return console.error("Error verifying payment:",e),n.NextResponse.json({error:"Failed to verify payment"},{status:500})}}let l=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/payments/verify/route",pathname:"/api/payments/verify",filename:"route",bundlePath:"app/api/payments/verify/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\api\\payments\\verify\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:y,serverHooks:x}=l;function v(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:y})}},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580,345,410],()=>t(94657));module.exports=s})();