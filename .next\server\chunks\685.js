"use strict";exports.id=685,exports.ids=[685],exports.modules={44493:(e,t,r)=>{r.d(t,{BT:()=>l,Wu:()=>u,ZB:()=>s,Zp:()=>o,aR:()=>i});var n=r(60687);r(43210);var a=r(4780);function o({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function s({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function u({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},56770:(e,t,r)=>{r.d(t,{tU:()=>q,av:()=>X,j7:()=>O,Xi:()=>W});var n=r(60687),a=r(43210),o=r(70569),i=r(11273),s=r(9510),l=r(98599),u=r(96963),d=r(14163),c=r(13495),f=r(65551),v=r(43),p="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},b="RovingFocusGroup",[g,x,h]=(0,s.N)(b),[w,y]=(0,i.A)(b,[h]),[j,R]=w(b),F=a.forwardRef((e,t)=>(0,n.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,n.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,n.jsx)(I,{...e,ref:t})})}));F.displayName=b;var I=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:s=!1,dir:u,currentTabStopId:g,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:w,onEntryFocus:y,preventScrollOnEntryFocus:R=!1,...F}=e,I=a.useRef(null),A=(0,l.s)(t,I),D=(0,v.jH)(u),[T,C]=(0,f.i)({prop:g,defaultProp:h??null,onChange:w,caller:b}),[k,E]=a.useState(!1),G=(0,c.c)(y),K=x(r),S=a.useRef(!1),[_,M]=a.useState(0);return a.useEffect(()=>{let e=I.current;if(e)return e.addEventListener(p,G),()=>e.removeEventListener(p,G)},[G]),(0,n.jsx)(j,{scope:r,orientation:i,dir:D,loop:s,currentTabStopId:T,onItemFocus:a.useCallback(e=>C(e),[C]),onItemShiftTab:a.useCallback(()=>E(!0),[]),onFocusableItemAdd:a.useCallback(()=>M(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>M(e=>e-1),[]),children:(0,n.jsx)(d.sG.div,{tabIndex:k||0===_?-1:0,"data-orientation":i,...F,ref:A,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{S.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!S.current;if(e.target===e.currentTarget&&t&&!k){let t=new CustomEvent(p,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=K().filter(e=>e.focusable);N([e.find(e=>e.active),e.find(e=>e.id===T),...e].filter(Boolean).map(e=>e.ref.current),R)}}S.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>E(!1))})})}),A="RovingFocusGroupItem",D=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:i=!0,active:s=!1,tabStopId:l,children:c,...f}=e,v=(0,u.B)(),p=l||v,m=R(A,r),b=m.currentTabStopId===p,h=x(r),{onFocusableItemAdd:w,onFocusableItemRemove:y,currentTabStopId:j}=m;return a.useEffect(()=>{if(i)return w(),()=>y()},[i,w,y]),(0,n.jsx)(g.ItemSlot,{scope:r,id:p,focusable:i,active:s,children:(0,n.jsx)(d.sG.span,{tabIndex:b?0:-1,"data-orientation":m.orientation,...f,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?m.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>m.onItemFocus(p)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let a=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(a))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(a)))return T[a]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=m.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>N(r))}}),children:"function"==typeof c?c({isCurrentTabStop:b,hasTabStop:null!=j}):c})})});D.displayName=A;var T={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function N(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var C=r(46059),k="Tabs",[E,G]=(0,i.A)(k,[y]),K=y(),[S,_]=E(k),M=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,onValueChange:o,defaultValue:i,orientation:s="horizontal",dir:l,activationMode:c="automatic",...p}=e,m=(0,v.jH)(l),[b,g]=(0,f.i)({prop:a,onChange:o,defaultProp:i??"",caller:k});return(0,n.jsx)(S,{scope:r,baseId:(0,u.B)(),value:b,onValueChange:g,orientation:s,dir:m,activationMode:c,children:(0,n.jsx)(d.sG.div,{dir:m,"data-orientation":s,...p,ref:t})})});M.displayName=k;var B="TabsList",L=a.forwardRef((e,t)=>{let{__scopeTabs:r,loop:a=!0,...o}=e,i=_(B,r),s=K(r);return(0,n.jsx)(F,{asChild:!0,...s,orientation:i.orientation,dir:i.dir,loop:a,children:(0,n.jsx)(d.sG.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:t})})});L.displayName=B;var P="TabsTrigger",z=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,disabled:i=!1,...s}=e,l=_(P,r),u=K(r),c=$(l.baseId,a),f=H(l.baseId,a),v=a===l.value;return(0,n.jsx)(D,{asChild:!0,...u,focusable:!i,active:v,children:(0,n.jsx)(d.sG.button,{type:"button",role:"tab","aria-selected":v,"aria-controls":f,"data-state":v?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:c,...s,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(a)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(a)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;v||i||!e||l.onValueChange(a)})})})});z.displayName=P;var U="TabsContent",V=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:o,forceMount:i,children:s,...l}=e,u=_(U,r),c=$(u.baseId,o),f=H(u.baseId,o),v=o===u.value,p=a.useRef(v);return a.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,n.jsx)(C.C,{present:i||v,children:({present:r})=>(0,n.jsx)(d.sG.div,{"data-state":v?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":c,hidden:!r,id:f,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:r&&s})})});function $(e,t){return`${e}-trigger-${t}`}function H(e,t){return`${e}-content-${t}`}V.displayName=U;var Z=r(4780);function q({className:e,...t}){return(0,n.jsx)(M,{"data-slot":"tabs",className:(0,Z.cn)("flex flex-col gap-2",e),...t})}function O({className:e,...t}){return(0,n.jsx)(L,{"data-slot":"tabs-list",className:(0,Z.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...t})}function W({className:e,...t}){return(0,n.jsx)(z,{"data-slot":"tabs-trigger",className:(0,Z.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}function X({className:e,...t}){return(0,n.jsx)(V,{"data-slot":"tabs-content",className:(0,Z.cn)("flex-1 outline-none",e),...t})}},70440:(e,t,r)=>{r.r(t),r.d(t,{default:()=>a});var n=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]}};