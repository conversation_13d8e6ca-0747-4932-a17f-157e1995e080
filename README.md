# KaleidoneX - Fullstack Next.js Application

A modern fullstack application built with Next.js 15, featuring authentication, payments, and a beautiful UI.

## 🚀 Tech Stack

- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **Authentication & Database**: Supabase
- **Payments**: Razorpay
- **Deployment**: Vercel

## ✨ Features

- 🔐 **Complete Authentication System**: User/Admin roles with secure login/signup
- 💳 **Payment Integration**: Razorpay payment gateway with order tracking
- 📱 **Responsive Design**: Mobile-first with device preview modes
- 🎨 **Advanced Customization**: 50+ template customization options
- 🔒 **Protected Routes**: Role-based access control with middleware
- 📊 **Admin Dashboard**: Comprehensive management panel with analytics
- 🛒 **Order Management**: Complete purchase and order tracking system
- 👤 **User Profiles**: Profile management with role-based features
- 🎯 **Live Preview**: Real-time template customization with responsive preview
- 📧 **Contact System**: Contact form with admin management
- 📈 **Visitor Analytics**: Track and analyze visitor behavior
- 🚀 **Production Ready**: Full-featured application ready for deployment

## 🛠️ Setup Instructions

### 1. Clone the repository

```bash
git clone <your-repo-url>
cd kaleidonex
```

### 2. Install dependencies

```bash
npm install
```

### 3. Environment Variables

Copy the example environment file and fill in your credentials:

```bash
cp .env.example .env.local
```

Fill in the following variables in `.env.local`:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Razorpay Configuration
NEXT_PUBLIC_RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 4. Supabase Setup

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to Settings > API to get your URL and keys
3. Run the following SQL in the Supabase SQL editor to create the required tables:

```sql
-- =====================================================
-- COMPREHENSIVE DATABASE SCHEMA FOR KALEIDONEX
-- =====================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- =====================================================
-- CORE USER MANAGEMENT TABLES
-- =====================================================

-- Enhanced profiles table with additional fields
CREATE TABLE profiles (
  id UUID REFERENCES auth.users ON DELETE CASCADE,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  username TEXT UNIQUE,
  full_name TEXT,
  avatar_url TEXT,
  website TEXT,
  bio TEXT,
  location TEXT,
  phone TEXT,
  date_of_birth DATE,
  role TEXT DEFAULT 'user' CHECK (role IN ('user', 'admin', 'moderator')),
  is_verified BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  last_login TIMESTAMP WITH TIME ZONE,
  preferences JSONB DEFAULT '{}',
  social_links JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (id)
);

-- User sessions for better tracking
CREATE TABLE user_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
  session_token TEXT NOT NULL UNIQUE,
  ip_address INET,
  user_agent TEXT,
  device_info JSONB,
  location_info JSONB,
  is_active BOOLEAN DEFAULT TRUE,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- TEMPLATE MANAGEMENT SYSTEM
-- =====================================================

-- Categories for better organization
CREATE TABLE categories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  slug TEXT NOT NULL UNIQUE,
  description TEXT,
  icon TEXT,
  color TEXT,
  parent_id UUID REFERENCES categories(id) ON DELETE SET NULL,
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  meta_data JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tags for flexible categorization
CREATE TABLE tags (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  slug TEXT NOT NULL UNIQUE,
  description TEXT,
  color TEXT,
  usage_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced templates table
CREATE TABLE templates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  description TEXT,
  long_description TEXT,
  price INTEGER NOT NULL DEFAULT 0,
  original_price INTEGER,
  discount_percentage INTEGER DEFAULT 0,
  category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
  preview_image TEXT,
  preview_images TEXT[] DEFAULT '{}',
  preview_url TEXT,
  demo_url TEXT,
  download_url TEXT,
  file_size BIGINT,
  version TEXT DEFAULT '1.0.0',
  compatibility TEXT[] DEFAULT '{}',
  features TEXT[] DEFAULT '{}',
  tech_stack TEXT[] DEFAULT '{}',
  difficulty_level TEXT DEFAULT 'beginner' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
  estimated_time TEXT,
  license_type TEXT DEFAULT 'standard',
  is_featured BOOLEAN DEFAULT FALSE,
  is_premium BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  is_free BOOLEAN DEFAULT FALSE,
  download_count INTEGER DEFAULT 0,
  view_count INTEGER DEFAULT 0,
  like_count INTEGER DEFAULT 0,
  rating_average DECIMAL(3,2) DEFAULT 0.00,
  rating_count INTEGER DEFAULT 0,
  author_id UUID REFERENCES auth.users ON DELETE SET NULL,
  meta_data JSONB DEFAULT '{}',
  seo_data JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Template tags relationship
CREATE TABLE template_tags (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  template_id UUID REFERENCES templates(id) ON DELETE CASCADE NOT NULL,
  tag_id UUID REFERENCES tags(id) ON DELETE CASCADE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(template_id, tag_id)
);

-- Template versions for version control
CREATE TABLE template_versions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  template_id UUID REFERENCES templates(id) ON DELETE CASCADE NOT NULL,
  version TEXT NOT NULL,
  changelog TEXT,
  download_url TEXT,
  file_size BIGINT,
  is_current BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- ENHANCED ORDER & PAYMENT SYSTEM
-- =====================================================

-- Enhanced orders table
CREATE TABLE orders (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  order_number TEXT NOT NULL UNIQUE,
  user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
  total_amount INTEGER NOT NULL,
  tax_amount INTEGER DEFAULT 0,
  discount_amount INTEGER DEFAULT 0,
  final_amount INTEGER NOT NULL,
  currency TEXT NOT NULL DEFAULT 'INR',
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded')),
  payment_method TEXT,
  payment_gateway TEXT DEFAULT 'razorpay',
  razorpay_order_id TEXT,
  razorpay_payment_id TEXT,
  razorpay_signature TEXT,
  billing_address JSONB,
  shipping_address JSONB,
  notes TEXT,
  metadata JSONB DEFAULT '{}',
  processed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Order items for detailed tracking
CREATE TABLE order_items (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE NOT NULL,
  template_id UUID REFERENCES templates(id) ON DELETE CASCADE NOT NULL,
  quantity INTEGER NOT NULL DEFAULT 1,
  unit_price INTEGER NOT NULL,
  total_price INTEGER NOT NULL,
  discount_amount INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced purchases table
CREATE TABLE purchases (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
  template_id UUID REFERENCES templates(id) ON DELETE CASCADE NOT NULL,
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
  amount INTEGER NOT NULL,
  currency TEXT NOT NULL DEFAULT 'INR',
  payment_id TEXT NOT NULL,
  payment_method TEXT,
  license_key TEXT UNIQUE,
  download_count INTEGER DEFAULT 0,
  max_downloads INTEGER DEFAULT 5,
  expires_at TIMESTAMP WITH TIME ZONE,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'expired', 'suspended', 'refunded')),
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- USER INTERACTION SYSTEM
-- =====================================================

-- Reviews and ratings
CREATE TABLE reviews (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
  template_id UUID REFERENCES templates(id) ON DELETE CASCADE NOT NULL,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  title TEXT,
  comment TEXT,
  pros TEXT[] DEFAULT '{}',
  cons TEXT[] DEFAULT '{}',
  is_verified_purchase BOOLEAN DEFAULT FALSE,
  is_featured BOOLEAN DEFAULT FALSE,
  helpful_count INTEGER DEFAULT 0,
  reported_count INTEGER DEFAULT 0,
  status TEXT DEFAULT 'published' CHECK (status IN ('draft', 'published', 'hidden', 'deleted')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, template_id)
);

-- Review helpfulness tracking
CREATE TABLE review_helpfulness (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  review_id UUID REFERENCES reviews(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
  is_helpful BOOLEAN NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(review_id, user_id)
);

-- User favorites/wishlist
CREATE TABLE favorites (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
  template_id UUID REFERENCES templates(id) ON DELETE CASCADE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, template_id)
);

-- User collections
CREATE TABLE collections (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  is_public BOOLEAN DEFAULT FALSE,
  cover_image TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Collection items
CREATE TABLE collection_items (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  collection_id UUID REFERENCES collections(id) ON DELETE CASCADE NOT NULL,
  template_id UUID REFERENCES templates(id) ON DELETE CASCADE NOT NULL,
  added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(collection_id, template_id)
);

-- =====================================================
-- ENHANCED CUSTOMIZATION SYSTEM
-- =====================================================

-- Enhanced customizations table
CREATE TABLE customizations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
  template_id UUID REFERENCES templates(id) ON DELETE SET NULL,
  name TEXT NOT NULL,
  description TEXT,
  navbar_style TEXT NOT NULL,
  hero_section TEXT NOT NULL,
  footer_style TEXT NOT NULL,
  config JSONB DEFAULT '{}',
  preview_image TEXT,
  is_public BOOLEAN DEFAULT FALSE,
  is_template BOOLEAN DEFAULT FALSE,
  version TEXT DEFAULT '1.0.0',
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Customization versions for history
CREATE TABLE customization_versions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  customization_id UUID REFERENCES customizations(id) ON DELETE CASCADE NOT NULL,
  version TEXT NOT NULL,
  config JSONB NOT NULL,
  changelog TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- COMMUNICATION SYSTEM
-- =====================================================

-- Enhanced contact requests
CREATE TABLE contact_requests (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  phone TEXT,
  subject TEXT,
  message TEXT NOT NULL,
  type TEXT DEFAULT 'general' CHECK (type IN ('general', 'support', 'sales', 'partnership', 'bug_report', 'feature_request')),
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  status TEXT DEFAULT 'new' CHECK (status IN ('new', 'in_progress', 'resolved', 'closed')),
  assigned_to UUID REFERENCES auth.users ON DELETE SET NULL,
  user_id UUID REFERENCES auth.users ON DELETE SET NULL,
  template_id UUID REFERENCES templates(id) ON DELETE SET NULL,
  metadata JSONB DEFAULT '{}',
  resolved_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Contact request responses
CREATE TABLE contact_responses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  contact_request_id UUID REFERENCES contact_requests(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
  message TEXT NOT NULL,
  is_internal BOOLEAN DEFAULT FALSE,
  attachments TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notifications system
CREATE TABLE notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type TEXT DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error')),
  category TEXT DEFAULT 'general' CHECK (category IN ('general', 'order', 'template', 'system', 'promotion')),
  action_url TEXT,
  action_text TEXT,
  is_read BOOLEAN DEFAULT FALSE,
  is_important BOOLEAN DEFAULT FALSE,
  expires_at TIMESTAMP WITH TIME ZONE,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  read_at TIMESTAMP WITH TIME ZONE
);

-- =====================================================
-- ANALYTICS & TRACKING SYSTEM
-- =====================================================

-- Enhanced visitor logs
CREATE TABLE visitor_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  session_id TEXT,
  user_id UUID REFERENCES auth.users ON DELETE SET NULL,
  ip_address INET,
  path TEXT NOT NULL,
  query_params JSONB,
  user_agent TEXT,
  referrer TEXT,
  screen_resolution TEXT,
  viewport_size TEXT,
  language TEXT,
  timezone TEXT,
  device_type TEXT,
  browser TEXT,
  browser_version TEXT,
  operating_system TEXT,
  os_version TEXT,
  country TEXT,
  region TEXT,
  city TEXT,
  page_title TEXT,
  load_time INTEGER,
  connection_type TEXT,
  is_bot BOOLEAN DEFAULT FALSE,
  utm_source TEXT,
  utm_medium TEXT,
  utm_campaign TEXT,
  utm_term TEXT,
  utm_content TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Page views tracking
CREATE TABLE page_views (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  template_id UUID REFERENCES templates(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users ON DELETE SET NULL,
  session_id TEXT,
  ip_address INET,
  user_agent TEXT,
  referrer TEXT,
  duration INTEGER,
  bounce BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Download tracking
CREATE TABLE downloads (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  template_id UUID REFERENCES templates(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
  purchase_id UUID REFERENCES purchases(id) ON DELETE SET NULL,
  ip_address INET,
  user_agent TEXT,
  file_size BIGINT,
  download_time INTEGER,
  status TEXT DEFAULT 'completed' CHECK (status IN ('started', 'completed', 'failed', 'cancelled')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
-- =====================================================
-- MARKETING & PROMOTION SYSTEM
-- =====================================================

-- Coupons and discounts
CREATE TABLE coupons (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  code TEXT NOT NULL UNIQUE,
  name TEXT NOT NULL,
  description TEXT,
  type TEXT NOT NULL CHECK (type IN ('percentage', 'fixed_amount', 'free_shipping')),
  value INTEGER NOT NULL,
  minimum_amount INTEGER DEFAULT 0,
  maximum_discount INTEGER,
  usage_limit INTEGER,
  usage_count INTEGER DEFAULT 0,
  user_limit INTEGER DEFAULT 1,
  is_active BOOLEAN DEFAULT TRUE,
  applies_to TEXT DEFAULT 'all' CHECK (applies_to IN ('all', 'specific_templates', 'categories')),
  template_ids UUID[] DEFAULT '{}',
  category_ids UUID[] DEFAULT '{}',
  starts_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Coupon usage tracking
CREATE TABLE coupon_usage (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  coupon_id UUID REFERENCES coupons(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE NOT NULL,
  discount_amount INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(coupon_id, user_id, order_id)
);

-- Email subscriptions
CREATE TABLE email_subscriptions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  user_id UUID REFERENCES auth.users ON DELETE CASCADE,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'unsubscribed', 'bounced')),
  preferences JSONB DEFAULT '{"newsletter": true, "promotions": true, "updates": true}',
  source TEXT DEFAULT 'website',
  confirmed_at TIMESTAMP WITH TIME ZONE,
  unsubscribed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- CONTENT MANAGEMENT SYSTEM
-- =====================================================

-- Blog posts
CREATE TABLE blog_posts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  excerpt TEXT,
  content TEXT NOT NULL,
  featured_image TEXT,
  author_id UUID REFERENCES auth.users ON DELETE SET NULL NOT NULL,
  category TEXT,
  tags TEXT[] DEFAULT '{}',
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
  is_featured BOOLEAN DEFAULT FALSE,
  view_count INTEGER DEFAULT 0,
  reading_time INTEGER,
  meta_title TEXT,
  meta_description TEXT,
  published_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- FAQ system
CREATE TABLE faqs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  question TEXT NOT NULL,
  answer TEXT NOT NULL,
  category TEXT DEFAULT 'general',
  sort_order INTEGER DEFAULT 0,
  is_featured BOOLEAN DEFAULT FALSE,
  view_count INTEGER DEFAULT 0,
  helpful_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- SYSTEM CONFIGURATION
-- =====================================================

-- Application settings
CREATE TABLE app_settings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  key TEXT NOT NULL UNIQUE,
  value JSONB NOT NULL,
  description TEXT,
  type TEXT DEFAULT 'string' CHECK (type IN ('string', 'number', 'boolean', 'json', 'array')),
  is_public BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Activity logs for audit trail
CREATE TABLE activity_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users ON DELETE SET NULL,
  action TEXT NOT NULL,
  resource_type TEXT NOT NULL,
  resource_id UUID,
  old_values JSONB,
  new_values JSONB,
  ip_address INET,
  user_agent TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- User and profile indexes
CREATE INDEX idx_profiles_role ON profiles(role);
CREATE INDEX idx_profiles_username ON profiles(username);
CREATE INDEX idx_profiles_is_active ON profiles(is_active);

-- Template indexes
CREATE INDEX idx_templates_category_id ON templates(category_id);
CREATE INDEX idx_templates_is_active ON templates(is_active);
CREATE INDEX idx_templates_is_featured ON templates(is_featured);
CREATE INDEX idx_templates_price ON templates(price);
CREATE INDEX idx_templates_rating_average ON templates(rating_average);
CREATE INDEX idx_templates_created_at ON templates(created_at);
CREATE INDEX idx_templates_slug ON templates(slug);

-- Order and purchase indexes
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_purchases_user_id ON purchases(user_id);
CREATE INDEX idx_purchases_template_id ON purchases(template_id);

-- Review indexes
CREATE INDEX idx_reviews_template_id ON reviews(template_id);
CREATE INDEX idx_reviews_rating ON reviews(rating);
CREATE INDEX idx_reviews_status ON reviews(status);

-- Analytics indexes
CREATE INDEX idx_visitor_logs_created_at ON visitor_logs(created_at);
CREATE INDEX idx_visitor_logs_path ON visitor_logs(path);
CREATE INDEX idx_visitor_logs_user_id ON visitor_logs(user_id);
CREATE INDEX idx_page_views_template_id ON page_views(template_id);

-- Contact and notification indexes
CREATE INDEX idx_contact_requests_status ON contact_requests(status);
CREATE INDEX idx_contact_requests_type ON contact_requests(type);
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);

-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE collections ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE purchases ENABLE ROW LEVEL SECURITY;
ALTER TABLE customizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE contact_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE visitor_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for profiles
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Admins can view all profiles" ON profiles FOR SELECT USING (
  EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
);

-- Create policies for templates
CREATE POLICY "Anyone can view active templates" ON templates FOR SELECT USING (is_active = true);
CREATE POLICY "Admins can manage templates" ON templates FOR ALL USING (
  EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
);

-- Create policies for orders
CREATE POLICY "Users can view own orders" ON orders FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create own orders" ON orders FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Admins can view all orders" ON orders FOR SELECT USING (
  EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
);

-- Create policies for purchases
CREATE POLICY "Users can view own purchases" ON purchases FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create own purchases" ON purchases FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Admins can view all purchases" ON purchases FOR SELECT USING (
  EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
);

-- Create policies for customizations
CREATE POLICY "Users can manage own customizations" ON customizations FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Anyone can view public customizations" ON customizations FOR SELECT USING (is_public = true);

-- Create policies for reviews
CREATE POLICY "Anyone can view published reviews" ON reviews FOR SELECT USING (status = 'published');
CREATE POLICY "Users can manage own reviews" ON reviews FOR ALL USING (auth.uid() = user_id);

-- Create policies for favorites
CREATE POLICY "Users can manage own favorites" ON favorites FOR ALL USING (auth.uid() = user_id);

-- Create policies for notifications
CREATE POLICY "Users can view own notifications" ON notifications FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own notifications" ON notifications FOR UPDATE USING (auth.uid() = user_id);

-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to relevant tables
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_templates_updated_at BEFORE UPDATE ON templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_purchases_updated_at BEFORE UPDATE ON purchases FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customizations_updated_at BEFORE UPDATE ON customizations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_contact_requests_updated_at BEFORE UPDATE ON contact_requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update template rating
CREATE OR REPLACE FUNCTION update_template_rating()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE templates
    SET
        rating_average = (
            SELECT ROUND(AVG(rating)::numeric, 2)
            FROM reviews
            WHERE template_id = COALESCE(NEW.template_id, OLD.template_id)
            AND status = 'published'
        ),
        rating_count = (
            SELECT COUNT(*)
            FROM reviews
            WHERE template_id = COALESCE(NEW.template_id, OLD.template_id)
            AND status = 'published'
        )
    WHERE id = COALESCE(NEW.template_id, OLD.template_id);
    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

-- Trigger to update template rating when reviews change
CREATE TRIGGER update_template_rating_trigger
    AFTER INSERT OR UPDATE OR DELETE ON reviews
    FOR EACH ROW EXECUTE FUNCTION update_template_rating();

-- Function to generate order number
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TRIGGER AS $$
BEGIN
    NEW.order_number = 'KX-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || LPAD(nextval('order_number_seq')::text, 6, '0');
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create sequence for order numbers
CREATE SEQUENCE order_number_seq START 1;

-- Trigger to generate order number
CREATE TRIGGER generate_order_number_trigger
    BEFORE INSERT ON orders
    FOR EACH ROW EXECUTE FUNCTION generate_order_number();

-- =====================================================
-- SAMPLE DATA FOR TESTING
-- =====================================================

-- Insert sample categories
INSERT INTO categories (name, slug, description, icon, color) VALUES
('Web Templates', 'web-templates', 'Complete website templates', 'Globe', '#3B82F6'),
('Landing Pages', 'landing-pages', 'High-converting landing pages', 'Zap', '#10B981'),
('E-commerce', 'ecommerce', 'Online store templates', 'ShoppingCart', '#F59E0B'),
('Dashboards', 'dashboards', 'Admin and analytics dashboards', 'BarChart3', '#8B5CF6'),
('Mobile Apps', 'mobile-apps', 'Mobile application templates', 'Smartphone', '#EF4444'),
('Components', 'components', 'Reusable UI components', 'Package', '#06B6D4');

-- Insert sample tags
INSERT INTO tags (name, slug, description, color) VALUES
('React', 'react', 'Built with React framework', '#61DAFB'),
('Next.js', 'nextjs', 'Built with Next.js framework', '#000000'),
('TypeScript', 'typescript', 'Written in TypeScript', '#3178C6'),
('Tailwind CSS', 'tailwind-css', 'Styled with Tailwind CSS', '#06B6D4'),
('Responsive', 'responsive', 'Mobile-friendly design', '#10B981'),
('Dark Mode', 'dark-mode', 'Supports dark mode', '#374151'),
('Animation', 'animation', 'Includes animations', '#F59E0B'),
('SEO Optimized', 'seo-optimized', 'Search engine optimized', '#8B5CF6');

-- Insert sample app settings
INSERT INTO app_settings (key, value, description, type, is_public) VALUES
('site_name', '"KaleidoneX"', 'Website name', 'string', true),
('site_description', '"Create stunning, customizable templates with our powerful design tools"', 'Website description', 'string', true),
('contact_email', '"<EMAIL>"', 'Contact email address', 'string', true),
('max_file_size', '52428800', 'Maximum file upload size in bytes (50MB)', 'number', false),
('allowed_file_types', '["jpg", "jpeg", "png", "gif", "pdf", "zip"]', 'Allowed file types for upload', 'array', false),
('payment_gateway', '"razorpay"', 'Default payment gateway', 'string', false),
('currency', '"INR"', 'Default currency', 'string', true),
('tax_rate', '18', 'Tax rate percentage', 'number', false),
('free_download_limit', '3', 'Free downloads per user', 'number', false),
('maintenance_mode', 'false', 'Enable maintenance mode', 'boolean', false);

-- Insert sample FAQs
INSERT INTO faqs (question, answer, category, sort_order, is_featured) VALUES
('How do I purchase a template?', 'Browse our template gallery, click on a template you like, and click the "Buy Now" button. You''ll be redirected to our secure payment gateway.', 'purchasing', 1, true),
('What payment methods do you accept?', 'We accept all major credit cards, debit cards, UPI, and net banking through Razorpay.', 'payment', 2, true),
('Can I customize the templates?', 'Yes! All our templates come with extensive customization options. You can modify colors, fonts, layouts, and content using our built-in customization tools.', 'customization', 3, true),
('Do you offer refunds?', 'We offer a 30-day money-back guarantee if you''re not satisfied with your purchase.', 'refunds', 4, false),
('How do I download my purchased templates?', 'After successful payment, you''ll receive a download link via email. You can also access your purchases from your account dashboard.', 'downloading', 5, false);
```

### 4. Insert Sample Templates (Optional)

After creating the database schema, you can insert some sample templates:

```sql
-- Insert sample templates
INSERT INTO templates (
  title, slug, description, long_description, price, category_id,
  preview_image, preview_url, features, tech_stack, difficulty_level,
  is_featured, is_active
) VALUES
(
  'Modern Business Landing Page',
  'modern-business-landing',
  'A sleek and professional landing page perfect for businesses',
  'This modern business landing page template features a clean design with smooth animations, responsive layout, and conversion-optimized sections. Perfect for startups, agencies, and professional services.',
  2999,
  (SELECT id FROM categories WHERE slug = 'landing-pages'),
  'https://images.unsplash.com/photo-*************-afdab827c52f?w=800',
  'https://demo.kaleidonex.com/modern-business',
  ARRAY['Responsive Design', 'Dark Mode', 'Contact Forms', 'SEO Optimized'],
  ARRAY['Next.js', 'TypeScript', 'Tailwind CSS'],
  'beginner',
  true,
  true
),
(
  'E-commerce Dashboard',
  'ecommerce-dashboard',
  'Complete admin dashboard for e-commerce management',
  'A comprehensive e-commerce dashboard with analytics, product management, order tracking, and customer management features. Built with modern technologies and best practices.',
  4999,
  (SELECT id FROM categories WHERE slug = 'dashboards'),
  'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800',
  'https://demo.kaleidonex.com/ecommerce-dashboard',
  ARRAY['Analytics Charts', 'Product Management', 'Order Tracking', 'User Management'],
  ARRAY['React', 'TypeScript', 'Tailwind CSS', 'Chart.js'],
  'intermediate',
  true,
  true
);
```

### 5. Create Admin User (Optional)

To create an admin user, first sign up normally, then run this SQL to make the user an admin:

```sql
-- Update user role to admin (replace email with actual admin email)
UPDATE profiles
SET role = 'admin'
WHERE id = (
  SELECT id FROM auth.users WHERE email = '<EMAIL>'
);
```

### 5. Razorpay Setup

1. Create an account at [razorpay.com](https://razorpay.com)
2. Go to Settings > API Keys to get your Key ID and Secret
3. Add the credentials to your `.env.local` file

### 6. Run the development server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## 🔐 Authentication System

### User Roles

The application supports two user roles:

- **User**: Regular users who can browse templates, make purchases, and customize templates
- **Admin**: Administrators who have access to the admin panel and can manage all data

### Authentication Features

- **Sign Up/Sign In**: Email and password authentication
- **Google OAuth**: Sign in with Google account
- **Password Reset**: Forgot password functionality with email reset
- **Role-based Access**: Different features based on user role
- **Protected Routes**: Automatic redirection based on authentication status
- **Session Management**: Persistent login sessions

### Demo Accounts

For testing purposes, you can use these demo accounts:

#### Admin Account
- **Email**: <EMAIL>
- **Password**: admin123
- **Access**: Full admin panel access

#### User Account
- **Email**: <EMAIL>
- **Password**: user123
- **Access**: Standard user features

### Quick Demo Access

The login page includes "Demo Login" buttons that automatically fill in the credentials for quick testing.

### Creating Admin Users

To make a user an admin:

1. Sign up with a regular account
2. Go to your Supabase dashboard
3. Navigate to Table Editor > profiles
4. Find the user and change their `role` from 'user' to 'admin'

Or run this SQL in Supabase:

```sql
UPDATE profiles
SET role = 'admin'
WHERE id = (SELECT id FROM auth.users WHERE email = '<EMAIL>');
```

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── (dashboard)/       # Dashboard routes
│   ├── api/               # API routes
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # React components
│   ├── layout/           # Layout components
│   ├── navigation/       # Navigation components
│   └── ui/               # shadcn/ui components
├── hooks/                # Custom React hooks
├── lib/                  # Utility libraries
│   ├── supabase/        # Supabase configuration
│   ├── database.types.ts # Database types
│   ├── razorpay.ts      # Razorpay configuration
│   └── utils.ts         # Utility functions
└── middleware.ts         # Next.js middleware
```

## 🚀 Deployment

### Deploy to Vercel

1. Push your code to GitHub
2. Connect your repository to [Vercel](https://vercel.com)
3. Add your environment variables in the Vercel dashboard
4. Deploy!

The application is optimized for Vercel deployment with automatic builds and deployments.

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 📝 License

This project is licensed under the MIT License.
