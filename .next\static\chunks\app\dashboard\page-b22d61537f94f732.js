(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{285:(e,s,t)=>{"use strict";t.d(s,{$:()=>c});var a=t(5155);t(2115);var r=t(9708),i=t(2085),n=t(9434);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:s,variant:t,size:i,asChild:c=!1,...l}=e,o=c?r.DX:"button";return(0,a.jsx)(o,{"data-slot":"button",className:(0,n.cn)(d({variant:t,size:i,className:s})),...l})}},381:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1007:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2643:(e,s,t)=>{"use strict";t.d(s,{U:()=>r});var a=t(1935);function r(){let e="https://aovrwjzhqrgbdhszdowg.supabase.co",s="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFvdnJ3anpocXJnYmRoc3pkb3dnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4NTI5MjEsImV4cCI6MjA2NDQyODkyMX0.9TWCiuDturnqdOSlDeOWVroegkTM7Nra-W2LUoyGDSs";if(!e||!s)throw Error("Missing Supabase environment variables. Please check your .env.local file.");return(0,a.createBrowserClient)(e,s)}},3199:(e,s,t)=>{Promise.resolve().then(t.bind(t,4879))},4879:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var a=t(5155),r=t(2115),i=t(6695),n=t(285),d=t(6126),c=t(2643),l=t(6671),o=t(5695),u=t(6151),h=t(381),x=t(7434),m=t(1007);function v(){var e;let[s,t]=(0,r.useState)(null),[v,p]=(0,r.useState)([]),[g,f]=(0,r.useState)([]),[j,b]=(0,r.useState)(!0),y=(0,c.U)(),N=(0,o.useRouter)();(0,r.useEffect)(()=>{w()},[]);let w=async()=>{try{let{data:{user:e},error:s}=await y.auth.getUser();if(s||!e)return void N.push("/login");t(e),await Promise.all([k(e.id),z(e.id)])}catch(e){console.error("Error checking user:",e),l.oR.error("Failed to load user data")}finally{b(!1)}},k=async e=>{try{let{data:s,error:t}=await y.from("purchases").select("\n          *,\n          templates (*)\n        ").eq("user_id",e).order("created_at",{ascending:!1});if(t)throw t;p(s||[])}catch(e){console.error("Error fetching purchases:",e),l.oR.error("Failed to load purchases")}},z=async e=>{try{let{data:s,error:t}=await y.from("customizations").select("*").eq("user_id",e).order("created_at",{ascending:!1});if(t)throw t;f(s||[])}catch(e){console.error("Error fetching customizations:",e),l.oR.error("Failed to load customizations")}},_=async()=>{await y.auth.signOut(),N.push("/")};return j?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Dashboard"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Loading your dashboard..."})]})}):s?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Dashboard"}),(0,a.jsxs)("p",{className:"text-muted-foreground",children:["Welcome back, ",(null==(e=s.user_metadata)?void 0:e.full_name)||s.email,"!"]})]}),(0,a.jsx)(n.$,{variant:"outline",onClick:_,children:"Sign Out"})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Templates Purchased"}),(0,a.jsx)(u.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:v.length})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Customizations"}),(0,a.jsx)(h.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:g.length})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Total Spent"}),(0,a.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"text-2xl font-bold",children:["₹",v.reduce((e,s)=>e+s.amount,0)]})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Account Status"}),(0,a.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:"Active"})})]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Your Purchased Templates"}),(0,a.jsx)(i.BT,{children:"Templates you have purchased and can download"})]}),(0,a.jsx)(i.Wu,{children:0===v.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("p",{className:"text-muted-foreground",children:"No templates purchased yet."}),(0,a.jsx)(n.$,{className:"mt-4",onClick:()=>N.push("/templates"),children:"Browse Templates"})]}):(0,a.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:v.map(e=>(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{className:"text-lg",children:e.templates.title}),(0,a.jsxs)(i.BT,{children:["Purchased on ",new Date(e.created_at).toLocaleDateString()]})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)(d.E,{variant:"secondary",children:e.templates.category}),(0,a.jsxs)("span",{className:"font-bold",children:["₹",e.amount]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.$,{size:"sm",className:"flex-1",children:"Download"}),e.templates.preview_url&&(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>window.open(e.templates.preview_url,"_blank"),children:"Preview"})]})]})]},e.id))})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Your Customizations"}),(0,a.jsx)(i.BT,{children:"Template customizations you have saved"})]}),(0,a.jsx)(i.Wu,{children:0===g.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("p",{className:"text-muted-foreground",children:"No customizations saved yet."}),(0,a.jsx)(n.$,{className:"mt-4",onClick:()=>N.push("/customize"),children:"Start Customizing"})]}):(0,a.jsx)("div",{className:"space-y-4",children:g.map(e=>(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"font-semibold mb-2",children:["Customization #",e.id.slice(0,8)]}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground space-y-1",children:[(0,a.jsxs)("p",{children:["Navbar: ",e.navbar_style]}),(0,a.jsxs)("p",{children:["Hero: ",e.hero_section]}),(0,a.jsxs)("p",{children:["Footer: ",e.footer_style]}),(0,a.jsxs)("p",{children:["Created: ",new Date(e.created_at).toLocaleDateString()]})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.$,{size:"sm",variant:"outline",onClick:()=>N.push("/customize"),children:"Edit"}),(0,a.jsx)(n.$,{size:"sm",children:"Use Template"})]})]})})},e.id))})})]})]}):null}},5695:(e,s,t)=>{"use strict";var a=t(8999);t.o(a,"usePathname")&&t.d(s,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(s,{useSearchParams:function(){return a.useSearchParams}})},6126:(e,s,t)=>{"use strict";t.d(s,{E:()=>c});var a=t(5155);t(2115);var r=t(9708),i=t(2085),n=t(9434);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:s,variant:t,asChild:i=!1,...c}=e,l=i?r.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(d({variant:t}),s),...c})}},6151:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("shopping-bag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},6695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>c,Wu:()=>l,ZB:()=>d,Zp:()=>i,aR:()=>n});var a=t(5155);t(2115);var r=t(9434);function i(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...t})}function n(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...t})}function d(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",s),...t})}function c(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",s),...t})}function l(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",s),...t})}},7434:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},9434:(e,s,t)=>{"use strict";t.d(s,{cn:()=>i});var a=t(2596),r=t(9688);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.QP)((0,a.$)(s))}}},e=>{var s=s=>e(e.s=s);e.O(0,[671,935,455,441,684,358],()=>s(3199)),_N_E=e.O()}]);