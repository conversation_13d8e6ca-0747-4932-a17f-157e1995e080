-- =====================================================
-- DATABASE DIAGNOSTIC SCRIPT FOR KALEIDONEX
-- =====================================================
-- Run these queries in your Supabase SQL editor to check your database structure

-- 1. Check if tables exist
SELECT table_name, table_type 
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- 2. Check table structures
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
ORDER BY table_name, ordinal_position;

-- 3. Check data counts in each table
SELECT 
    'profiles' as table_name, 
    COUNT(*) as row_count 
FROM profiles
UNION ALL
SELECT 
    'templates' as table_name, 
    COUNT(*) as row_count 
FROM templates
UNION ALL
SELECT 
    'purchases' as table_name, 
    COUNT(*) as row_count 
FROM purchases
UNION ALL
SELECT 
    'customizations' as table_name, 
    COUNT(*) as row_count 
FROM customizations
UNION ALL
SELECT 
    'visitor_logs' as table_name, 
    COUNT(*) as row_count 
FROM visitor_logs
UNION ALL
SELECT 
    'contact_requests' as table_name, 
    COUNT(*) as row_count 
FROM contact_requests;

-- 4. Check specific table structures
-- Purchases table structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'purchases' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Customizations table structure  
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'customizations' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Visitor_logs table structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'visitor_logs' AND table_schema = 'public'
ORDER BY ordinal_position;

-- 5. Sample data from each table (first 3 rows)
-- Purchases sample
SELECT * FROM purchases LIMIT 3;

-- Customizations sample  
SELECT * FROM customizations LIMIT 3;

-- Visitor logs sample
SELECT * FROM visitor_logs LIMIT 3;

-- 6. Check for foreign key relationships
SELECT
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_schema = 'public'
ORDER BY tc.table_name;

-- 7. Check RLS policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- 8. Check if enhanced schema is applied
-- This will help determine if you're using old or new schema
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'templates' AND column_name = 'slug') 
        THEN 'Enhanced schema detected'
        ELSE 'Basic schema detected'
    END as schema_status;

-- 9. Check specific missing columns that might cause issues
SELECT 
    'templates' as table_name,
    CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'templates' AND column_name = 'slug') THEN 'EXISTS' ELSE 'MISSING' END as slug_column,
    CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'templates' AND column_name = 'original_price') THEN 'EXISTS' ELSE 'MISSING' END as original_price_column,
    CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'templates' AND column_name = 'category_id') THEN 'EXISTS' ELSE 'MISSING' END as category_id_column
UNION ALL
SELECT 
    'purchases' as table_name,
    CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'purchases' AND column_name = 'payment_id') THEN 'EXISTS' ELSE 'MISSING' END as payment_id_column,
    CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'purchases' AND column_name = 'order_id') THEN 'EXISTS' ELSE 'MISSING' END as order_id_column,
    CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'purchases' AND column_name = 'license_key') THEN 'EXISTS' ELSE 'MISSING' END as license_key_column;
