"use strict";exports.id=81,exports.ids=[81],exports.modules={3589:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},13964:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},18853:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(43210),o=n(66156);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},42762:(e,t,n)=>{n.d(t,{UC:()=>t3,In:()=>t6,q7:()=>t9,VF:()=>t4,p4:()=>t7,ZL:()=>t5,bL:()=>t0,wn:()=>nt,PP:()=>ne,l9:()=>t1,WT:()=>t2,LM:()=>t8});var r=n(43210),o=n(51215),i=n(67969),l=n(70569),a=n(9510),s=n(98599),u=n(11273),c=n(43),f=n(31355),d=n(1359),p=n(32547),h=n(96963);let m=["top","right","bottom","left"],g=Math.min,v=Math.max,w=Math.round,y=Math.floor,x=e=>({x:e,y:e}),b={left:"right",right:"left",bottom:"top",top:"bottom"},S={start:"end",end:"start"};function C(e,t){return"function"==typeof e?e(t):e}function R(e){return e.split("-")[0]}function A(e){return e.split("-")[1]}function T(e){return"x"===e?"y":"x"}function P(e){return"y"===e?"height":"width"}function j(e){return["top","bottom"].includes(R(e))?"y":"x"}function E(e){return e.replace(/start|end/g,e=>S[e])}function L(e){return e.replace(/left|right|bottom|top/g,e=>b[e])}function k(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function N(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function D(e,t,n){let r,{reference:o,floating:i}=e,l=j(t),a=T(j(t)),s=P(a),u=R(t),c="y"===l,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,p=o[s]/2-i[s]/2;switch(u){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(A(t)){case"start":r[a]-=p*(n&&c?-1:1);break;case"end":r[a]+=p*(n&&c?-1:1)}return r}let M=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),s=await (null==l.isRTL?void 0:l.isRTL(t)),u=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:f}=D(u,r,s),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:m}=a[n],{x:g,y:v,data:w,reset:y}=await m({x:c,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:u,platform:l,elements:{reference:e,floating:t}});c=null!=g?g:c,f=null!=v?v:f,p={...p,[i]:{...p[i],...w}},y&&h<=50&&(h++,"object"==typeof y&&(y.placement&&(d=y.placement),y.rects&&(u=!0===y.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):y.rects),{x:c,y:f}=D(u,d,s)),n=-1)}return{x:c,y:f,placement:d,strategy:o,middlewareData:p}};async function H(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:d=!1,padding:p=0}=C(t,e),h=k(p),m=a[d?"floating"===f?"reference":"floating":f],g=N(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:s})),v="floating"===f?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),y=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},x=N(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:v,offsetParent:w,strategy:s}):v);return{top:(g.top-x.top+h.top)/y.y,bottom:(x.bottom-g.bottom+h.bottom)/y.y,left:(g.left-x.left+h.left)/y.x,right:(x.right-g.right+h.right)/y.x}}function O(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function I(e){return m.some(t=>e[t]>=0)}async function B(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=R(n),a=A(n),s="y"===j(n),u=["left","top"].includes(l)?-1:1,c=i&&s?-1:1,f=C(t,e),{mainAxis:d,crossAxis:p,alignmentAxis:h}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),s?{x:p*c,y:d*u}:{x:d*u,y:p*c}}function F(){return"undefined"!=typeof window}function V(e){return _(e)?(e.nodeName||"").toLowerCase():"#document"}function W(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function z(e){var t;return null==(t=(_(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function _(e){return!!F()&&(e instanceof Node||e instanceof W(e).Node)}function G(e){return!!F()&&(e instanceof Element||e instanceof W(e).Element)}function $(e){return!!F()&&(e instanceof HTMLElement||e instanceof W(e).HTMLElement)}function K(e){return!!F()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof W(e).ShadowRoot)}function q(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=J(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function U(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function X(e){let t=Y(),n=G(e)?J(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function Y(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function Z(e){return["html","body","#document"].includes(V(e))}function J(e){return W(e).getComputedStyle(e)}function Q(e){return G(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ee(e){if("html"===V(e))return e;let t=e.assignedSlot||e.parentNode||K(e)&&e.host||z(e);return K(t)?t.host:t}function et(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=ee(t);return Z(n)?t.ownerDocument?t.ownerDocument.body:t.body:$(n)&&q(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=W(o);if(i){let e=en(l);return t.concat(l,l.visualViewport||[],q(o)?o:[],e&&n?et(e):[])}return t.concat(o,et(o,[],n))}function en(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function er(e){let t=J(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=$(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=w(n)!==i||w(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function eo(e){return G(e)?e:e.contextElement}function ei(e){let t=eo(e);if(!$(t))return x(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=er(t),l=(i?w(n.width):n.width)/r,a=(i?w(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let el=x(0);function ea(e){let t=W(e);return Y()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:el}function es(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=eo(e),a=x(1);t&&(r?G(r)&&(a=ei(r)):a=ei(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===W(l))&&o)?ea(l):x(0),u=(i.left+s.x)/a.x,c=(i.top+s.y)/a.y,f=i.width/a.x,d=i.height/a.y;if(l){let e=W(l),t=r&&G(r)?W(r):r,n=e,o=en(n);for(;o&&r&&t!==n;){let e=ei(o),t=o.getBoundingClientRect(),r=J(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,c*=e.y,f*=e.x,d*=e.y,u+=i,c+=l,o=en(n=W(o))}}return N({width:f,height:d,x:u,y:c})}function eu(e,t){let n=Q(e).scrollLeft;return t?t.left+n:es(z(e)).left+n}function ec(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eu(e,r)),y:r.top+t.scrollTop}}function ef(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=W(e),r=z(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,s=0;if(o){i=o.width,l=o.height;let e=Y();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:i,height:l,x:a,y:s}}(e,n);else if("document"===t)r=function(e){let t=z(e),n=Q(e),r=e.ownerDocument.body,o=v(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=v(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+eu(e),a=-n.scrollTop;return"rtl"===J(r).direction&&(l+=v(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}(z(e));else if(G(t))r=function(e,t){let n=es(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=$(e)?ei(e):x(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=ea(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return N(r)}function ed(e){return"static"===J(e).position}function ep(e,t){if(!$(e)||"fixed"===J(e).position)return null;if(t)return t(e);let n=e.offsetParent;return z(e)===n&&(n=n.ownerDocument.body),n}function eh(e,t){let n=W(e);if(U(e))return n;if(!$(e)){let t=ee(e);for(;t&&!Z(t);){if(G(t)&&!ed(t))return t;t=ee(t)}return n}let r=ep(e,t);for(;r&&["table","td","th"].includes(V(r))&&ed(r);)r=ep(r,t);return r&&Z(r)&&ed(r)&&!X(r)?n:r||function(e){let t=ee(e);for(;$(t)&&!Z(t);){if(X(t))return t;if(U(t))break;t=ee(t)}return null}(e)||n}let em=async function(e){let t=this.getOffsetParent||eh,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=$(t),o=z(t),i="fixed"===n,l=es(e,!0,i,t),a={scrollLeft:0,scrollTop:0},s=x(0);if(r||!r&&!i)if(("body"!==V(t)||q(o))&&(a=Q(t)),r){let e=es(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=eu(o));i&&!r&&o&&(s.x=eu(o));let u=!o||r||i?x(0):ec(o,a);return{x:l.left+a.scrollLeft-s.x-u.x,y:l.top+a.scrollTop-s.y-u.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eg={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=z(r),a=!!t&&U(t.floating);if(r===l||a&&i)return n;let s={scrollLeft:0,scrollTop:0},u=x(1),c=x(0),f=$(r);if((f||!f&&!i)&&(("body"!==V(r)||q(l))&&(s=Q(r)),$(r))){let e=es(r);u=ei(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let d=!l||f||i?x(0):ec(l,s,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-s.scrollLeft*u.x+c.x+d.x,y:n.y*u.y-s.scrollTop*u.y+c.y+d.y}},getDocumentElement:z,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?U(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=et(e,[],!1).filter(e=>G(e)&&"body"!==V(e)),o=null,i="fixed"===J(e).position,l=i?ee(e):e;for(;G(l)&&!Z(l);){let t=J(l),n=X(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||q(l)&&!n&&function e(t,n){let r=ee(t);return!(r===n||!G(r)||Z(r))&&("fixed"===J(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=ee(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((e,n)=>{let r=ef(t,n,o);return e.top=v(r.top,e.top),e.right=g(r.right,e.right),e.bottom=g(r.bottom,e.bottom),e.left=v(r.left,e.left),e},ef(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eh,getElementRects:em,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=er(e);return{width:t,height:n}},getScale:ei,isElement:G,isRTL:function(e){return"rtl"===J(e).direction}};function ev(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ew=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:s}=t,{element:u,padding:c=0}=C(e,t)||{};if(null==u)return{};let f=k(c),d={x:n,y:r},p=T(j(o)),h=P(p),m=await l.getDimensions(u),w="y"===p,y=w?"clientHeight":"clientWidth",x=i.reference[h]+i.reference[p]-d[p]-i.floating[h],b=d[p]-i.reference[p],S=await (null==l.getOffsetParent?void 0:l.getOffsetParent(u)),R=S?S[y]:0;R&&await (null==l.isElement?void 0:l.isElement(S))||(R=a.floating[y]||i.floating[h]);let E=R/2-m[h]/2-1,L=g(f[w?"top":"left"],E),N=g(f[w?"bottom":"right"],E),D=R-m[h]-N,M=R/2-m[h]/2+(x/2-b/2),H=v(L,g(M,D)),O=!s.arrow&&null!=A(o)&&M!==H&&i.reference[h]/2-(M<L?L:N)-m[h]/2<0,I=O?M<L?M-L:M-D:0;return{[p]:d[p]+I,data:{[p]:H,centerOffset:M-H-I,...O&&{alignmentOffset:I}},reset:O}}}),ey=(e,t,n)=>{let r=new Map,o={platform:eg,...n},i={...o.platform,_c:r};return M(e,t,{...o,platform:i})};var ex="undefined"!=typeof document?r.useLayoutEffect:function(){};function eb(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eb(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eb(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eS(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eC(e,t){let n=eS(e);return Math.round(t*n)/n}function eR(e){let t=r.useRef(e);return ex(()=>{t.current=e}),t}let eA=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ew({element:n.current,padding:r}).fn(t):{}:n?ew({element:n,padding:r}).fn(t):{}}}),eT=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,s=await B(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:l}}}}}(e),options:[e,t]}),eP=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=C(e,t),u={x:n,y:r},c=await H(t,s),f=j(R(o)),d=T(f),p=u[d],h=u[f];if(i){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=p+c[e],r=p-c[t];p=v(n,g(p,r))}if(l){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=h+c[e],r=h-c[t];h=v(n,g(h,r))}let m=a.fn({...t,[d]:p,[f]:h});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[d]:i,[f]:l}}}}}}(e),options:[e,t]}),ej=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=C(e,t),c={x:n,y:r},f=j(o),d=T(f),p=c[d],h=c[f],m=C(a,t),g="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(s){let e="y"===d?"height":"width",t=i.reference[d]-i.floating[e]+g.mainAxis,n=i.reference[d]+i.reference[e]-g.mainAxis;p<t?p=t:p>n&&(p=n)}if(u){var v,w;let e="y"===d?"width":"height",t=["top","left"].includes(R(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(v=l.offset)?void 0:v[f])||0)+(t?0:g.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(w=l.offset)?void 0:w[f])||0)-(t?g.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[d]:p,[f]:h}}}}(e),options:[e,t]}),eE=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:s,rects:u,initialPlacement:c,platform:f,elements:d}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:w=!0,...y}=C(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let x=R(a),b=j(c),S=R(c)===c,k=await (null==f.isRTL?void 0:f.isRTL(d.floating)),N=m||(S||!w?[L(c)]:function(e){let t=L(e);return[E(e),t,E(t)]}(c)),D="none"!==v;!m&&D&&N.push(...function(e,t,n,r){let o=A(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(R(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(E)))),i}(c,w,v,k));let M=[c,...N],O=await H(t,y),I=[],B=(null==(r=s.flip)?void 0:r.overflows)||[];if(p&&I.push(O[x]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=A(e),o=T(j(e)),i=P(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=L(l)),[l,L(l)]}(a,u,k);I.push(O[e[0]],O[e[1]])}if(B=[...B,{placement:a,overflows:I}],!I.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=M[e];if(t&&("alignment"!==h||b===j(t)||B.every(e=>e.overflows[0]>0&&j(e.placement)===b)))return{data:{index:e,overflows:B},reset:{placement:t}};let n=null==(i=B.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(g){case"bestFit":{let e=null==(l=B.filter(e=>{if(D){let t=j(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eL=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i,{placement:l,rects:a,platform:s,elements:u}=t,{apply:c=()=>{},...f}=C(e,t),d=await H(t,f),p=R(l),h=A(l),m="y"===j(l),{width:w,height:y}=a.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==s.isRTL?void 0:s.isRTL(u.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let x=y-d.top-d.bottom,b=w-d.left-d.right,S=g(y-d[o],x),T=g(w-d[i],b),P=!t.middlewareData.shift,E=S,L=T;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(L=b),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(E=x),P&&!h){let e=v(d.left,0),t=v(d.right,0),n=v(d.top,0),r=v(d.bottom,0);m?L=w-2*(0!==e||0!==t?e+t:v(d.left,d.right)):E=y-2*(0!==n||0!==r?n+r:v(d.top,d.bottom))}await c({...t,availableWidth:L,availableHeight:E});let k=await s.getDimensions(u.floating);return w!==k.width||y!==k.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ek=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=C(e,t);switch(r){case"referenceHidden":{let e=O(await H(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:I(e)}}}case"escaped":{let e=O(await H(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:I(e)}}}default:return{}}}}}(e),options:[e,t]}),eN=(e,t)=>({...eA(e),options:[e,t]});var eD=n(14163),eM=n(60687),eH=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eM.jsx)(eD.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eM.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eH.displayName="Arrow";var eO=n(13495),eI=n(66156),eB=n(18853),eF="Popper",[eV,eW]=(0,u.A)(eF),[ez,e_]=eV(eF),eG=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eM.jsx)(ez,{scope:t,anchor:o,onAnchorChange:i,children:n})};eG.displayName=eF;var e$="PopperAnchor",eK=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,l=e_(e$,n),a=r.useRef(null),u=(0,s.s)(t,a);return r.useEffect(()=>{l.onAnchorChange(o?.current||a.current)}),o?null:(0,eM.jsx)(eD.sG.div,{...i,ref:u})});eK.displayName=e$;var eq="PopperContent",[eU,eX]=eV(eq),eY=r.forwardRef((e,t)=>{let{__scopePopper:n,side:i="bottom",sideOffset:l=0,align:a="center",alignOffset:u=0,arrowPadding:c=0,avoidCollisions:f=!0,collisionBoundary:d=[],collisionPadding:p=0,sticky:h="partial",hideWhenDetached:m=!1,updatePositionStrategy:w="optimized",onPlaced:x,...b}=e,S=e_(eq,n),[C,R]=r.useState(null),A=(0,s.s)(t,e=>R(e)),[T,P]=r.useState(null),j=(0,eB.X)(T),E=j?.width??0,L=j?.height??0,k="number"==typeof p?p:{top:0,right:0,bottom:0,left:0,...p},N=Array.isArray(d)?d:[d],D=N.length>0,M={padding:k,boundary:N.filter(e0),altBoundary:D},{refs:H,floatingStyles:O,placement:I,isPositioned:B,middlewareData:F}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:l,elements:{reference:a,floating:s}={},transform:u=!0,whileElementsMounted:c,open:f}=e,[d,p]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,m]=r.useState(i);eb(h,i)||m(i);let[g,v]=r.useState(null),[w,y]=r.useState(null),x=r.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),b=r.useCallback(e=>{e!==A.current&&(A.current=e,y(e))},[]),S=a||g,C=s||w,R=r.useRef(null),A=r.useRef(null),T=r.useRef(d),P=null!=c,j=eR(c),E=eR(l),L=eR(f),k=r.useCallback(()=>{if(!R.current||!A.current)return;let e={placement:t,strategy:n,middleware:h};E.current&&(e.platform=E.current),ey(R.current,A.current,e).then(e=>{let t={...e,isPositioned:!1!==L.current};N.current&&!eb(T.current,t)&&(T.current=t,o.flushSync(()=>{p(t)}))})},[h,t,n,E,L]);ex(()=>{!1===f&&T.current.isPositioned&&(T.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[f]);let N=r.useRef(!1);ex(()=>(N.current=!0,()=>{N.current=!1}),[]),ex(()=>{if(S&&(R.current=S),C&&(A.current=C),S&&C){if(j.current)return j.current(S,C,k);k()}},[S,C,k,j,P]);let D=r.useMemo(()=>({reference:R,floating:A,setReference:x,setFloating:b}),[x,b]),M=r.useMemo(()=>({reference:S,floating:C}),[S,C]),H=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!M.floating)return e;let t=eC(M.floating,d.x),r=eC(M.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...eS(M.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,M.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:k,refs:D,elements:M,floatingStyles:H}),[d,k,D,M,H])}({strategy:"fixed",placement:i+("center"!==a?"-"+a:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:u=!1}=r,c=eo(e),f=i||l?[...c?et(c):[],...et(t)]:[];f.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let d=c&&s?function(e,t){let n,r=null,o=z(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function l(a,s){void 0===a&&(a=!1),void 0===s&&(s=1),i();let u=e.getBoundingClientRect(),{left:c,top:f,width:d,height:p}=u;if(a||t(),!d||!p)return;let h=y(f),m=y(o.clientWidth-(c+d)),w={rootMargin:-h+"px "+-m+"px "+-y(o.clientHeight-(f+p))+"px "+-y(c)+"px",threshold:v(0,g(1,s))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==s){if(!x)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||ev(u,e.getBoundingClientRect())||l(),x=!1}try{r=new IntersectionObserver(b,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),i}(c,n):null,p=-1,h=null;a&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===c&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),c&&!u&&h.observe(c),h.observe(t));let m=u?es(e):null;return u&&function t(){let r=es(e);m&&!ev(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;f.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==d||d(),null==(e=h)||e.disconnect(),h=null,u&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===w}),elements:{reference:S.anchor},middleware:[eT({mainAxis:l+L,alignmentAxis:u}),f&&eP({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?ej():void 0,...M}),f&&eE({...M}),eL({...M,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),T&&eN({element:T,padding:c}),e1({arrowWidth:E,arrowHeight:L}),m&&ek({strategy:"referenceHidden",...M})]}),[V,W]=e2(I),_=(0,eO.c)(x);(0,eI.N)(()=>{B&&_?.()},[B,_]);let G=F.arrow?.x,$=F.arrow?.y,K=F.arrow?.centerOffset!==0,[q,U]=r.useState();return(0,eI.N)(()=>{C&&U(window.getComputedStyle(C).zIndex)},[C]),(0,eM.jsx)("div",{ref:H.setFloating,"data-radix-popper-content-wrapper":"",style:{...O,transform:B?O.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:q,"--radix-popper-transform-origin":[F.transformOrigin?.x,F.transformOrigin?.y].join(" "),...F.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eM.jsx)(eU,{scope:n,placedSide:V,onArrowChange:P,arrowX:G,arrowY:$,shouldHideArrow:K,children:(0,eM.jsx)(eD.sG.div,{"data-side":V,"data-align":W,...b,ref:A,style:{...b.style,animation:B?void 0:"none"}})})})});eY.displayName=eq;var eZ="PopperArrow",eJ={top:"bottom",right:"left",bottom:"top",left:"right"},eQ=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eX(eZ,n),i=eJ[o.placedSide];return(0,eM.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eM.jsx)(eH,{...r,ref:t,style:{...r.style,display:"block"}})})});function e0(e){return null!==e}eQ.displayName=eZ;var e1=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[s,u]=e2(n),c={start:"0%",center:"50%",end:"100%"}[u],f=(o.arrow?.x??0)+l/2,d=(o.arrow?.y??0)+a/2,p="",h="";return"bottom"===s?(p=i?c:`${f}px`,h=`${-a}px`):"top"===s?(p=i?c:`${f}px`,h=`${r.floating.height+a}px`):"right"===s?(p=`${-a}px`,h=i?c:`${d}px`):"left"===s&&(p=`${r.floating.width+a}px`,h=i?c:`${d}px`),{data:{x:p,y:h}}}});function e2(e){let[t,n="center"]=e.split("-");return[t,n]}var e6=n(25028),e5=n(8730),e3=n(65551),e8=n(83721),e9=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});r.forwardRef((e,t)=>(0,eM.jsx)(eD.sG.span,{...e,ref:t,style:{...e9,...e.style}})).displayName="VisuallyHidden";var e7=n(63376),e4=n(42247),te=[" ","Enter","ArrowUp","ArrowDown"],tt=[" ","Enter"],tn="Select",[tr,to,ti]=(0,a.N)(tn),[tl,ta]=(0,u.A)(tn,[ti,eW]),ts=eW(),[tu,tc]=tl(tn),[tf,td]=tl(tn),tp=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:i,onOpenChange:l,value:a,defaultValue:s,onValueChange:u,dir:f,name:d,autoComplete:p,disabled:m,required:g,form:v}=e,w=ts(t),[y,x]=r.useState(null),[b,S]=r.useState(null),[C,R]=r.useState(!1),A=(0,c.jH)(f),[T,P]=(0,e3.i)({prop:o,defaultProp:i??!1,onChange:l,caller:tn}),[j,E]=(0,e3.i)({prop:a,defaultProp:s,onChange:u,caller:tn}),L=r.useRef(null),k=!y||v||!!y.closest("form"),[N,D]=r.useState(new Set),M=Array.from(N).map(e=>e.props.value).join(";");return(0,eM.jsx)(eG,{...w,children:(0,eM.jsxs)(tu,{required:g,scope:t,trigger:y,onTriggerChange:x,valueNode:b,onValueNodeChange:S,valueNodeHasChildren:C,onValueNodeHasChildrenChange:R,contentId:(0,h.B)(),value:j,onValueChange:E,open:T,onOpenChange:P,dir:A,triggerPointerDownPosRef:L,disabled:m,children:[(0,eM.jsx)(tr.Provider,{scope:t,children:(0,eM.jsx)(tf,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{D(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{D(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),k?(0,eM.jsxs)(tY,{"aria-hidden":!0,required:g,tabIndex:-1,name:d,autoComplete:p,value:j,onChange:e=>E(e.target.value),disabled:m,form:v,children:[void 0===j?(0,eM.jsx)("option",{value:""}):null,Array.from(N)]},M):null]})})};tp.displayName=tn;var th="SelectTrigger",tm=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...i}=e,a=ts(n),u=tc(th,n),c=u.disabled||o,f=(0,s.s)(t,u.onTriggerChange),d=to(n),p=r.useRef("touch"),[h,m,g]=tJ(e=>{let t=d().filter(e=>!e.disabled),n=t.find(e=>e.value===u.value),r=tQ(t,e,n);void 0!==r&&u.onValueChange(r.value)}),v=e=>{c||(u.onOpenChange(!0),g()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,eM.jsx)(eK,{asChild:!0,...a,children:(0,eM.jsx)(eD.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":tZ(u.value)?"":void 0,...i,ref:f,onClick:(0,l.m)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&v(e)}),onPointerDown:(0,l.m)(i.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(v(e),e.preventDefault())}),onKeyDown:(0,l.m)(i.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&te.includes(e.key)&&(v(),e.preventDefault())})})})});tm.displayName=th;var tg="SelectValue",tv=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,u=tc(tg,n),{onValueNodeHasChildrenChange:c}=u,f=void 0!==i,d=(0,s.s)(t,u.onValueNodeChange);return(0,eI.N)(()=>{c(f)},[c,f]),(0,eM.jsx)(eD.sG.span,{...a,ref:d,style:{pointerEvents:"none"},children:tZ(u.value)?(0,eM.jsx)(eM.Fragment,{children:l}):i})});tv.displayName=tg;var tw=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,eM.jsx)(eD.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});tw.displayName="SelectIcon";var ty=e=>(0,eM.jsx)(e6.Z,{asChild:!0,...e});ty.displayName="SelectPortal";var tx="SelectContent",tb=r.forwardRef((e,t)=>{let n=tc(tx,e.__scopeSelect),[i,l]=r.useState();return((0,eI.N)(()=>{l(new DocumentFragment)},[]),n.open)?(0,eM.jsx)(tA,{...e,ref:t}):i?o.createPortal((0,eM.jsx)(tS,{scope:e.__scopeSelect,children:(0,eM.jsx)(tr.Slot,{scope:e.__scopeSelect,children:(0,eM.jsx)("div",{children:e.children})})}),i):null});tb.displayName=tx;var[tS,tC]=tl(tx),tR=(0,e5.TL)("SelectContent.RemoveScroll"),tA=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:u,side:c,sideOffset:h,align:m,alignOffset:g,arrowPadding:v,collisionBoundary:w,collisionPadding:y,sticky:x,hideWhenDetached:b,avoidCollisions:S,...C}=e,R=tc(tx,n),[A,T]=r.useState(null),[P,j]=r.useState(null),E=(0,s.s)(t,e=>T(e)),[L,k]=r.useState(null),[N,D]=r.useState(null),M=to(n),[H,O]=r.useState(!1),I=r.useRef(!1);r.useEffect(()=>{if(A)return(0,e7.Eq)(A)},[A]),(0,d.Oh)();let B=r.useCallback(e=>{let[t,...n]=M().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(n?.scrollIntoView({block:"nearest"}),n===t&&P&&(P.scrollTop=0),n===r&&P&&(P.scrollTop=P.scrollHeight),n?.focus(),document.activeElement!==o))return},[M,P]),F=r.useCallback(()=>B([L,A]),[B,L,A]);r.useEffect(()=>{H&&F()},[H,F]);let{onOpenChange:V,triggerPointerDownPosRef:W}=R;r.useEffect(()=>{if(A){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(W.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(W.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():A.contains(n.target)||V(!1),document.removeEventListener("pointermove",t),W.current=null};return null!==W.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[A,V,W]),r.useEffect(()=>{let e=()=>V(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[V]);let[z,_]=tJ(e=>{let t=M().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=tQ(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),G=r.useCallback((e,t,n)=>{let r=!I.current&&!n;(void 0!==R.value&&R.value===t||r)&&(k(e),r&&(I.current=!0))},[R.value]),$=r.useCallback(()=>A?.focus(),[A]),K=r.useCallback((e,t,n)=>{let r=!I.current&&!n;(void 0!==R.value&&R.value===t||r)&&D(e)},[R.value]),q="popper"===o?tP:tT,U=q===tP?{side:c,sideOffset:h,align:m,alignOffset:g,arrowPadding:v,collisionBoundary:w,collisionPadding:y,sticky:x,hideWhenDetached:b,avoidCollisions:S}:{};return(0,eM.jsx)(tS,{scope:n,content:A,viewport:P,onViewportChange:j,itemRefCallback:G,selectedItem:L,onItemLeave:$,itemTextRefCallback:K,focusSelectedItem:F,selectedItemText:N,position:o,isPositioned:H,searchRef:z,children:(0,eM.jsx)(e4.A,{as:tR,allowPinchZoom:!0,children:(0,eM.jsx)(p.n,{asChild:!0,trapped:R.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,l.m)(i,e=>{R.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,eM.jsx)(f.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>R.onOpenChange(!1),children:(0,eM.jsx)(q,{role:"listbox",id:R.contentId,"data-state":R.open?"open":"closed",dir:R.dir,onContextMenu:e=>e.preventDefault(),...C,...U,onPlaced:()=>O(!0),ref:E,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:(0,l.m)(C.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||_(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=M().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>B(t)),e.preventDefault()}})})})})})})});tA.displayName="SelectContentImpl";var tT=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...l}=e,a=tc(tx,n),u=tC(tx,n),[c,f]=r.useState(null),[d,p]=r.useState(null),h=(0,s.s)(t,e=>p(e)),m=to(n),g=r.useRef(!1),v=r.useRef(!0),{viewport:w,selectedItem:y,selectedItemText:x,focusSelectedItem:b}=u,S=r.useCallback(()=>{if(a.trigger&&a.valueNode&&c&&d&&w&&y&&x){let e=a.trigger.getBoundingClientRect(),t=d.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=x.getBoundingClientRect();if("rtl"!==a.dir){let o=r.left-t.left,l=n.left-o,a=e.left-l,s=e.width+a,u=Math.max(s,t.width),f=window.innerWidth-10,d=(0,i.q)(l,[10,Math.max(10,f-u)]);c.style.minWidth=s+"px",c.style.left=d+"px"}else{let o=t.right-r.right,l=window.innerWidth-n.right-o,a=window.innerWidth-e.right-l,s=e.width+a,u=Math.max(s,t.width),f=window.innerWidth-10,d=(0,i.q)(l,[10,Math.max(10,f-u)]);c.style.minWidth=s+"px",c.style.right=d+"px"}let l=m(),s=window.innerHeight-20,u=w.scrollHeight,f=window.getComputedStyle(d),p=parseInt(f.borderTopWidth,10),h=parseInt(f.paddingTop,10),v=parseInt(f.borderBottomWidth,10),b=p+h+u+parseInt(f.paddingBottom,10)+v,S=Math.min(5*y.offsetHeight,b),C=window.getComputedStyle(w),R=parseInt(C.paddingTop,10),A=parseInt(C.paddingBottom,10),T=e.top+e.height/2-10,P=y.offsetHeight/2,j=p+h+(y.offsetTop+P);if(j<=T){let e=l.length>0&&y===l[l.length-1].ref.current;c.style.bottom="0px";let t=Math.max(s-T,P+(e?A:0)+(d.clientHeight-w.offsetTop-w.offsetHeight)+v);c.style.height=j+t+"px"}else{let e=l.length>0&&y===l[0].ref.current;c.style.top="0px";let t=Math.max(T,p+w.offsetTop+(e?R:0)+P);c.style.height=t+(b-j)+"px",w.scrollTop=j-T+w.offsetTop}c.style.margin="10px 0",c.style.minHeight=S+"px",c.style.maxHeight=s+"px",o?.(),requestAnimationFrame(()=>g.current=!0)}},[m,a.trigger,a.valueNode,c,d,w,y,x,a.dir,o]);(0,eI.N)(()=>S(),[S]);let[C,R]=r.useState();(0,eI.N)(()=>{d&&R(window.getComputedStyle(d).zIndex)},[d]);let A=r.useCallback(e=>{e&&!0===v.current&&(S(),b?.(),v.current=!1)},[S,b]);return(0,eM.jsx)(tj,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:g,onScrollButtonChange:A,children:(0,eM.jsx)("div",{ref:f,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:C},children:(0,eM.jsx)(eD.sG.div,{...l,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});tT.displayName="SelectItemAlignedPosition";var tP=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=ts(n);return(0,eM.jsx)(eY,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});tP.displayName="SelectPopperPosition";var[tj,tE]=tl(tx,{}),tL="SelectViewport",tk=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...i}=e,a=tC(tL,n),u=tE(tL,n),c=(0,s.s)(t,a.onViewportChange),f=r.useRef(0);return(0,eM.jsxs)(eM.Fragment,{children:[(0,eM.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,eM.jsx)(tr.Slot,{scope:n,children:(0,eM.jsx)(eD.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,l.m)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=u;if(r?.current&&n){let e=Math.abs(f.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}f.current=t.scrollTop})})})]})});tk.displayName=tL;var tN="SelectGroup",[tD,tM]=tl(tN);r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,h.B)();return(0,eM.jsx)(tD,{scope:n,id:o,children:(0,eM.jsx)(eD.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})}).displayName=tN;var tH="SelectLabel";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tM(tH,n);return(0,eM.jsx)(eD.sG.div,{id:o.id,...r,ref:t})}).displayName=tH;var tO="SelectItem",[tI,tB]=tl(tO),tF=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:i=!1,textValue:a,...u}=e,c=tc(tO,n),f=tC(tO,n),d=c.value===o,[p,m]=r.useState(a??""),[g,v]=r.useState(!1),w=(0,s.s)(t,e=>f.itemRefCallback?.(e,o,i)),y=(0,h.B)(),x=r.useRef("touch"),b=()=>{i||(c.onValueChange(o),c.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,eM.jsx)(tI,{scope:n,value:o,disabled:i,textId:y,isSelected:d,onItemTextChange:r.useCallback(e=>{m(t=>t||(e?.textContent??"").trim())},[]),children:(0,eM.jsx)(tr.ItemSlot,{scope:n,value:o,disabled:i,textValue:p,children:(0,eM.jsx)(eD.sG.div,{role:"option","aria-labelledby":y,"data-highlighted":g?"":void 0,"aria-selected":d&&g,"data-state":d?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...u,ref:w,onFocus:(0,l.m)(u.onFocus,()=>v(!0)),onBlur:(0,l.m)(u.onBlur,()=>v(!1)),onClick:(0,l.m)(u.onClick,()=>{"mouse"!==x.current&&b()}),onPointerUp:(0,l.m)(u.onPointerUp,()=>{"mouse"===x.current&&b()}),onPointerDown:(0,l.m)(u.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:(0,l.m)(u.onPointerMove,e=>{x.current=e.pointerType,i?f.onItemLeave?.():"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,l.m)(u.onPointerLeave,e=>{e.currentTarget===document.activeElement&&f.onItemLeave?.()}),onKeyDown:(0,l.m)(u.onKeyDown,e=>{(f.searchRef?.current===""||" "!==e.key)&&(tt.includes(e.key)&&b()," "===e.key&&e.preventDefault())})})})})});tF.displayName=tO;var tV="SelectItemText",tW=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:i,style:l,...a}=e,u=tc(tV,n),c=tC(tV,n),f=tB(tV,n),d=td(tV,n),[p,h]=r.useState(null),m=(0,s.s)(t,e=>h(e),f.onItemTextChange,e=>c.itemTextRefCallback?.(e,f.value,f.disabled)),g=p?.textContent,v=r.useMemo(()=>(0,eM.jsx)("option",{value:f.value,disabled:f.disabled,children:g},f.value),[f.disabled,f.value,g]),{onNativeOptionAdd:w,onNativeOptionRemove:y}=d;return(0,eI.N)(()=>(w(v),()=>y(v)),[w,y,v]),(0,eM.jsxs)(eM.Fragment,{children:[(0,eM.jsx)(eD.sG.span,{id:f.textId,...a,ref:m}),f.isSelected&&u.valueNode&&!u.valueNodeHasChildren?o.createPortal(a.children,u.valueNode):null]})});tW.displayName=tV;var tz="SelectItemIndicator",t_=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return tB(tz,n).isSelected?(0,eM.jsx)(eD.sG.span,{"aria-hidden":!0,...r,ref:t}):null});t_.displayName=tz;var tG="SelectScrollUpButton",t$=r.forwardRef((e,t)=>{let n=tC(tG,e.__scopeSelect),o=tE(tG,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,eI.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,eM.jsx)(tU,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});t$.displayName=tG;var tK="SelectScrollDownButton",tq=r.forwardRef((e,t)=>{let n=tC(tK,e.__scopeSelect),o=tE(tK,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,eI.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,eM.jsx)(tU,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});tq.displayName=tK;var tU=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...i}=e,a=tC("SelectScrollButton",n),s=r.useRef(null),u=to(n),c=r.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return r.useEffect(()=>()=>c(),[c]),(0,eI.N)(()=>{let e=u().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[u]),(0,eM.jsx)(eD.sG.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,l.m)(i.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(o,50))}),onPointerMove:(0,l.m)(i.onPointerMove,()=>{a.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(o,50))}),onPointerLeave:(0,l.m)(i.onPointerLeave,()=>{c()})})});r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,eM.jsx)(eD.sG.div,{"aria-hidden":!0,...r,ref:t})}).displayName="SelectSeparator";var tX="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=ts(n),i=tc(tX,n),l=tC(tX,n);return i.open&&"popper"===l.position?(0,eM.jsx)(eQ,{...o,...r,ref:t}):null}).displayName=tX;var tY=r.forwardRef(({__scopeSelect:e,value:t,...n},o)=>{let i=r.useRef(null),l=(0,s.s)(o,i),a=(0,e8.Z)(t);return r.useEffect(()=>{let e=i.current;if(!e)return;let n=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==t&&n){let r=new Event("change",{bubbles:!0});n.call(e,t),e.dispatchEvent(r)}},[a,t]),(0,eM.jsx)(eD.sG.select,{...n,style:{...e9,...n.style},ref:l,defaultValue:t})});function tZ(e){return""===e||void 0===e}function tJ(e){let t=(0,eO.c)(e),n=r.useRef(""),o=r.useRef(0),i=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),l=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,i,l]}function tQ(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,a=(r=e,o=Math.max(l,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(a=a.filter(e=>e!==n));let s=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return s!==n?s:void 0}tY.displayName="SelectBubbleInput";var t0=tp,t1=tm,t2=tv,t6=tw,t5=ty,t3=tb,t8=tk,t9=tF,t7=tW,t4=t_,ne=t$,nt=tq},67969:(e,t,n)=>{n.d(t,{q:()=>r});function r(e,[t,n]){return Math.min(n,Math.max(t,e))}},78272:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},83721:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(43210);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}}};