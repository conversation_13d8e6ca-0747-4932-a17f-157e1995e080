"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { createClient } from "@/lib/supabase/client"
import { toast } from "sonner"

export default function AdminDebugPage() {
  const [results, setResults] = useState<any>({})
  const [loading, setLoading] = useState(false)
  const supabase = createClient()

  const testDatabaseConnection = async () => {
    setLoading(true)
    const testResults: any = {}

    try {
      // Test 1: Check if we can connect to Supabase
      console.log('Testing Supabase connection...')
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      testResults.connection = userError ? `Error: ${userError.message}` : 'Connected successfully'
      testResults.user = user ? `Logged in as: ${user.email}` : 'Not logged in'

      // Test 2: Check tables exist and get counts
      console.log('Testing table access...')
      
      // Test templates table
      try {
        const { data: templates, error: templatesError } = await supabase
          .from('templates')
          .select('*', { count: 'exact', head: true })
        testResults.templates = templatesError 
          ? `Error: ${templatesError.message}` 
          : `Found ${templates?.length || 0} templates`
      } catch (error: any) {
        testResults.templates = `Error: ${error.message}`
      }

      // Test purchases table
      try {
        const { data: purchases, error: purchasesError } = await supabase
          .from('purchases')
          .select('*', { count: 'exact', head: true })
        testResults.purchases = purchasesError 
          ? `Error: ${purchasesError.message}` 
          : `Found ${purchases?.length || 0} purchases`
      } catch (error: any) {
        testResults.purchases = `Error: ${error.message}`
      }

      // Test customizations table
      try {
        const { data: customizations, error: customizationsError } = await supabase
          .from('customizations')
          .select('*', { count: 'exact', head: true })
        testResults.customizations = customizationsError 
          ? `Error: ${customizationsError.message}` 
          : `Found ${customizations?.length || 0} customizations`
      } catch (error: any) {
        testResults.customizations = `Error: ${error.message}`
      }

      // Test visitor_logs table
      try {
        const { data: visitorLogs, error: visitorLogsError } = await supabase
          .from('visitor_logs')
          .select('*', { count: 'exact', head: true })
        testResults.visitor_logs = visitorLogsError 
          ? `Error: ${visitorLogsError.message}` 
          : `Found ${visitorLogs?.length || 0} visitor logs`
      } catch (error: any) {
        testResults.visitor_logs = `Error: ${error.message}`
      }

      // Test contact_requests table
      try {
        const { data: contacts, error: contactsError } = await supabase
          .from('contact_requests')
          .select('*', { count: 'exact', head: true })
        testResults.contact_requests = contactsError 
          ? `Error: ${contactsError.message}` 
          : `Found ${contacts?.length || 0} contact requests`
      } catch (error: any) {
        testResults.contact_requests = `Error: ${error.message}`
      }

      // Test 3: Try to get actual data samples
      console.log('Testing data retrieval...')
      
      try {
        const { data: samplePurchases, error: sampleError } = await supabase
          .from('purchases')
          .select('*')
          .limit(1)
        
        if (sampleError) {
          testResults.sample_purchase = `Error: ${sampleError.message}`
        } else if (samplePurchases && samplePurchases.length > 0) {
          testResults.sample_purchase = `Sample data: ${JSON.stringify(samplePurchases[0], null, 2)}`
        } else {
          testResults.sample_purchase = 'No purchase data found'
        }
      } catch (error: any) {
        testResults.sample_purchase = `Error: ${error.message}`
      }

      try {
        const { data: sampleCustomizations, error: sampleError } = await supabase
          .from('customizations')
          .select('*')
          .limit(1)
        
        if (sampleError) {
          testResults.sample_customization = `Error: ${sampleError.message}`
        } else if (sampleCustomizations && sampleCustomizations.length > 0) {
          testResults.sample_customization = `Sample data: ${JSON.stringify(sampleCustomizations[0], null, 2)}`
        } else {
          testResults.sample_customization = 'No customization data found'
        }
      } catch (error: any) {
        testResults.sample_customization = `Error: ${error.message}`
      }

      // Test 4: Check admin access
      try {
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', user?.id)
          .single()
        
        testResults.admin_access = profileError 
          ? `Error: ${profileError.message}` 
          : `Role: ${profile?.role || 'No role set'}`
      } catch (error: any) {
        testResults.admin_access = `Error: ${error.message}`
      }

      setResults(testResults)
      toast.success('Database tests completed')
    } catch (error: any) {
      console.error('Test error:', error)
      testResults.general_error = error.message
      setResults(testResults)
      toast.error('Database tests failed')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Admin Panel Debug</h1>
        <p className="text-muted-foreground">
          This page helps diagnose issues with the admin panel data loading
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Database Connection Test</CardTitle>
          <CardDescription>
            Test database connectivity and data access
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={testDatabaseConnection} disabled={loading}>
            {loading ? 'Testing...' : 'Run Database Tests'}
          </Button>
        </CardContent>
      </Card>

      {Object.keys(results).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(results).map(([key, value]) => (
                <div key={key} className="border rounded p-3">
                  <h4 className="font-semibold capitalize">{key.replace(/_/g, ' ')}</h4>
                  <pre className="text-sm text-muted-foreground mt-1 whitespace-pre-wrap">
                    {String(value)}
                  </pre>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p><strong>1.</strong> Click "Run Database Tests" to check your database</p>
            <p><strong>2.</strong> Check the results for any errors</p>
            <p><strong>3.</strong> If you see table errors, run the database-schema.sql file in Supabase</p>
            <p><strong>4.</strong> If you see permission errors, check your RLS policies</p>
            <p><strong>5.</strong> If you see "No data found", add some test data using sample-data.sql</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
