(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[78],{285:(e,t,s)=>{"use strict";s.d(t,{$:()=>d});var a=s(5155);s(2115);var r=s(9708),n=s(2085),i=s(9434);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:s,size:n,asChild:d=!1,...c}=e,o=d?r.DX:"button";return(0,a.jsx)(o,{"data-slot":"button",className:(0,i.cn)(l({variant:s,size:n,className:t})),...c})}},381:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1285:(e,t,s)=>{"use strict";s.d(t,{B:()=>d});var a,r=s(2115),n=s(2712),i=(a||(a=s.t(r,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function d(e){let[t,s]=r.useState(i());return(0,n.N)(()=>{e||s(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},1492:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]])},2525:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2712:(e,t,s)=>{"use strict";s.d(t,{N:()=>r});var a=s(2115),r=globalThis?.document?a.useLayoutEffect:()=>{}},3655:(e,t,s)=>{"use strict";s.d(t,{hO:()=>d,sG:()=>l});var a=s(2115),r=s(7650),n=s(9708),i=s(5155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let s=(0,n.TL)(`Primitive.${t}`),r=a.forwardRef((e,a)=>{let{asChild:r,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(r?s:t,{...n,ref:a})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function d(e,t){e&&r.flushSync(()=>e.dispatchEvent(t))}},5185:(e,t,s)=>{"use strict";function a(e,t,{checkForDefaultPrevented:s=!0}={}){return function(a){if(e?.(a),!1===s||!a.defaultPrevented)return t?.(a)}}s.d(t,{m:()=>a})},5490:(e,t,s)=>{Promise.resolve().then(s.bind(s,9304))},5845:(e,t,s)=>{"use strict";s.d(t,{i:()=>l});var a,r=s(2115),n=s(2712),i=(a||(a=s.t(r,2)))[" useInsertionEffect ".trim().toString()]||n.N;function l({prop:e,defaultProp:t,onChange:s=()=>{},caller:a}){let[n,l,d]=function({defaultProp:e,onChange:t}){let[s,a]=r.useState(e),n=r.useRef(s),l=r.useRef(t);return i(()=>{l.current=t},[t]),r.useEffect(()=>{n.current!==s&&(l.current?.(s),n.current=s)},[s,n]),[s,a,l]}({defaultProp:t,onChange:s}),c=void 0!==e,o=c?e:n;{let t=r.useRef(void 0!==e);r.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${a} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,a])}return[o,r.useCallback(t=>{if(c){let s="function"==typeof t?t(e):t;s!==e&&d.current?.(s)}else l(t)},[c,e,l,d])]}Symbol("RADIX:SYNC_STATE")},6081:(e,t,s)=>{"use strict";s.d(t,{A:()=>i,q:()=>n});var a=s(2115),r=s(5155);function n(e,t){let s=a.createContext(t),n=e=>{let{children:t,...n}=e,i=a.useMemo(()=>n,Object.values(n));return(0,r.jsx)(s.Provider,{value:i,children:t})};return n.displayName=e+"Provider",[n,function(r){let n=a.useContext(s);if(n)return n;if(void 0!==t)return t;throw Error(`\`${r}\` must be used within \`${e}\``)}]}function i(e,t=[]){let s=[],n=()=>{let t=s.map(e=>a.createContext(e));return function(s){let r=s?.[e]||t;return a.useMemo(()=>({[`__scope${e}`]:{...s,[e]:r}}),[s,r])}};return n.scopeName=e,[function(t,n){let i=a.createContext(n),l=s.length;s=[...s,n];let d=t=>{let{scope:s,children:n,...d}=t,c=s?.[e]?.[l]||i,o=a.useMemo(()=>d,Object.values(d));return(0,r.jsx)(c.Provider,{value:o,children:n})};return d.displayName=t+"Provider",[d,function(s,r){let d=r?.[e]?.[l]||i,c=a.useContext(d);if(c)return c;if(void 0!==n)return n;throw Error(`\`${s}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let s=()=>{let s=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=s.reduce((t,{useScope:s,scopeName:a})=>{let r=s(e)[`__scope${a}`];return{...t,...r}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return s.scopeName=t.scopeName,s}(n,...t)]}},6126:(e,t,s)=>{"use strict";s.d(t,{E:()=>d});var a=s(5155);s(2115);var r=s(9708),n=s(2085),i=s(9434);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:s,asChild:n=!1,...d}=e,c=n?r.DX:"span";return(0,a.jsx)(c,{"data-slot":"badge",className:(0,i.cn)(l({variant:s}),t),...d})}},6151:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("shopping-bag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},6695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>i});var a=s(5155);s(2115);var r=s(9434);function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...s})}},7313:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>d,av:()=>c,j7:()=>l,tU:()=>i});var a=s(5155);s(2115);var r=s(64),n=s(9434);function i(e){let{className:t,...s}=e;return(0,a.jsx)(r.bL,{"data-slot":"tabs",className:(0,n.cn)("flex flex-col gap-2",t),...s})}function l(e){let{className:t,...s}=e;return(0,a.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,n.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,n.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,n.cn)("flex-1 outline-none",t),...s})}},7580:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9033:(e,t,s)=>{"use strict";s.d(t,{c:()=>r});var a=s(2115);function r(e){let t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...e)=>t.current?.(...e),[])}},9304:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var a=s(5155),r=s(2115),n=s(6695),i=s(285),l=s(6126),d=s(7313),c=s(6671),o=s(8883),u=s(6151),m=s(381),x=s(7580),h=s(1788),p=s(1492),v=s(2525);let f=[{id:"1",name:"John Doe",email:"<EMAIL>",message:"I'm interested in your premium templates. Can you provide more information about pricing?",created_at:"2024-01-15T10:30:00Z"},{id:"2",name:"Jane Smith",email:"<EMAIL>",message:"I need a custom template for my e-commerce business. Please contact me.",created_at:"2024-01-14T15:45:00Z"}],g=[{id:"1",user_id:"user1",template_id:"template1",amount:2999,currency:"INR",razorpay_payment_id:"pay_123456789",status:"completed",created_at:"2024-01-15T12:00:00Z",templates:{title:"Modern Business Template",category:"Business"},profiles:{full_name:"John Doe"}},{id:"2",user_id:"user2",template_id:"template2",amount:1999,currency:"INR",razorpay_payment_id:"pay_987654321",status:"completed",created_at:"2024-01-14T09:30:00Z",templates:{title:"Creative Portfolio",category:"Portfolio"},profiles:{full_name:"Jane Smith"}}],j=[{id:"1",user_id:"user1",navbar_style:"modern",hero_section:"hero1",footer_style:"simple",created_at:"2024-01-15T14:20:00Z",updated_at:"2024-01-15T14:20:00Z",profiles:{full_name:"John Doe"}},{id:"2",user_id:"user2",navbar_style:"classic",hero_section:"hero2",footer_style:"detailed",created_at:"2024-01-14T11:15:00Z",updated_at:"2024-01-14T11:15:00Z",profiles:{full_name:"Jane Smith"}}],b=[{id:"1",ip_address:"*************",path:"/",user_agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",created_at:"2024-01-15T16:30:00Z"},{id:"2",ip_address:"*************",path:"/templates",user_agent:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",created_at:"2024-01-15T16:25:00Z"},{id:"3",ip_address:"*************",path:"/customize",user_agent:"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36",created_at:"2024-01-15T16:20:00Z"}];function N(){let[e,t]=(0,r.useState)("contacts"),s=(e,t,s)=>{let a=new Blob([[s,...e.map(e=>s.map(t=>{var s,a,r,n,i;switch(t){case"Name":return e.name||(null==(s=e.profiles)?void 0:s.full_name)||"Unknown";case"Email":return e.email||"N/A";case"Message":return e.message||"N/A";case"User Email":return(null==(a=e.profiles)?void 0:a.full_name)||"Unknown";case"Template":return(null==(r=e.templates)?void 0:r.title)||"N/A";case"Amount":return(null==(n=e.amount)?void 0:n.toString())||"N/A";case"Currency":return e.currency||"N/A";case"Status":return e.status||"N/A";case"Payment ID":return e.razorpay_payment_id||"N/A";case"User":return(null==(i=e.profiles)?void 0:i.full_name)||"Unknown";case"Navbar Style":return e.navbar_style||"N/A";case"Hero Section":return e.hero_section||"N/A";case"Footer Style":return e.footer_style||"N/A";case"IP Address":return e.ip_address||"Unknown";case"Path":return e.path||"N/A";case"User Agent":return e.user_agent||"Unknown";case"Created At":return new Date(e.created_at).toLocaleString();case"Updated At":return new Date(e.updated_at||e.created_at).toLocaleString();default:return"N/A"}}))].map(e=>e.map(e=>'"'.concat(e,'"')).join(",")).join("\n")],{type:"text/csv"}),r=window.URL.createObjectURL(a),n=document.createElement("a");n.href=r,n.download="".concat(t,"-").concat(new Date().toISOString().split("T")[0],".csv"),n.click(),window.URL.revokeObjectURL(r),c.oR.success("".concat(t," exported successfully!"))},N=(e,t)=>{c.oR.success("".concat(t," deleted successfully! (Demo mode)"))};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Admin Panel (Demo)"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"This is a demo of the admin panel functionality with sample data"})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Contact Requests"}),(0,a.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:f.length})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Total Purchases"}),(0,a.jsx)(u.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:g.length})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Customizations"}),(0,a.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:j.length})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Visitor Logs"}),(0,a.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:b.length})})]})]}),(0,a.jsxs)(d.tU,{value:e,onValueChange:t,children:[(0,a.jsxs)(d.j7,{className:"grid w-full grid-cols-4",children:[(0,a.jsx)(d.Xi,{value:"contacts",children:"Contact Requests"}),(0,a.jsx)(d.Xi,{value:"purchases",children:"Purchases"}),(0,a.jsx)(d.Xi,{value:"customizations",children:"Customizations"}),(0,a.jsx)(d.Xi,{value:"visitors",children:"Visitor Logs"})]}),(0,a.jsx)(d.av,{value:"contacts",className:"space-y-4",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(n.ZB,{children:"Contact Requests"}),(0,a.jsx)(n.BT,{children:"Manage customer inquiries and messages"})]}),(0,a.jsx)("div",{className:"flex gap-2",children:(0,a.jsxs)(i.$,{variant:"outline",onClick:()=>s(f,"contact-requests",["Name","Email","Message","Created At"]),children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Export CSV"]})})]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:f.map(e=>(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.email})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(i.$,{variant:"outline",size:"sm",children:(0,a.jsx)(p.A,{className:"h-4 w-4"})}),(0,a.jsx)(i.$,{variant:"destructive",size:"sm",onClick:()=>N(e.id,"Contact request"),children:(0,a.jsx)(v.A,{className:"h-4 w-4"})})]})]}),(0,a.jsx)("p",{className:"text-sm mb-2",children:e.message}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:new Date(e.created_at).toLocaleString()})]},e.id))})})]})}),(0,a.jsx)(d.av,{value:"purchases",className:"space-y-4",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(n.ZB,{children:"Purchases"}),(0,a.jsxs)(n.BT,{children:["Total Revenue: ₹",g.reduce((e,t)=>e+t.amount,0)," • ",g.length," purchases"]})]}),(0,a.jsx)("div",{className:"flex gap-2",children:(0,a.jsxs)(i.$,{variant:"outline",onClick:()=>s(g,"purchases",["User Email","Template","Amount","Currency","Status","Payment ID","Created At"]),children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Export CSV"]})})]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:g.map(e=>(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold",children:e.templates.title}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.profiles.full_name})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(i.$,{variant:"outline",size:"sm",children:(0,a.jsx)(p.A,{className:"h-4 w-4"})}),(0,a.jsx)(i.$,{variant:"destructive",size:"sm",onClick:()=>N(e.id,"Purchase"),children:(0,a.jsx)(v.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Amount:"}),(0,a.jsxs)("p",{className:"font-medium",children:["₹",e.amount]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Status:"}),(0,a.jsx)(l.E,{variant:"default",children:e.status})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Payment ID:"}),(0,a.jsx)("p",{className:"font-mono text-xs",children:e.razorpay_payment_id})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Date:"}),(0,a.jsx)("p",{children:new Date(e.created_at).toLocaleDateString()})]})]})]},e.id))})})]})}),(0,a.jsx)(d.av,{value:"customizations",className:"space-y-4",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(n.ZB,{children:"Customizations"}),(0,a.jsx)(n.BT,{children:"User template customization sessions"})]}),(0,a.jsx)("div",{className:"flex gap-2",children:(0,a.jsxs)(i.$,{variant:"outline",onClick:()=>s(j,"customizations",["User","Navbar Style","Hero Section","Footer Style","Created At","Updated At"]),children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Export CSV"]})})]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:j.map(e=>(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold",children:e.profiles.full_name}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["ID: ",e.id]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(i.$,{variant:"outline",size:"sm",children:(0,a.jsx)(p.A,{className:"h-4 w-4"})}),(0,a.jsx)(i.$,{variant:"destructive",size:"sm",onClick:()=>N(e.id,"Customization"),children:(0,a.jsx)(v.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Navbar:"}),(0,a.jsx)("p",{className:"font-medium",children:e.navbar_style})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Hero:"}),(0,a.jsx)("p",{className:"font-medium",children:e.hero_section})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Footer:"}),(0,a.jsx)("p",{className:"font-medium",children:e.footer_style})]})]}),(0,a.jsxs)("div",{className:"mt-2 text-xs text-muted-foreground",children:["Created: ",new Date(e.created_at).toLocaleString()," • Updated: ",new Date(e.updated_at).toLocaleString()]})]},e.id))})})]})}),(0,a.jsx)(d.av,{value:"visitors",className:"space-y-4",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(n.ZB,{children:"Visitor Logs"}),(0,a.jsxs)(n.BT,{children:[b.length," visits • ",new Set(b.map(e=>e.ip_address)).size," unique IPs"]})]}),(0,a.jsx)("div",{className:"flex gap-2",children:(0,a.jsxs)(i.$,{variant:"outline",onClick:()=>s(b,"visitor-logs",["IP Address","Path","User Agent","Created At"]),children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Export CSV"]})})]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:b.map(e=>(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold",children:e.path}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["IP: ",e.ip_address]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(i.$,{variant:"outline",size:"sm",children:(0,a.jsx)(p.A,{className:"h-4 w-4"})}),(0,a.jsx)(i.$,{variant:"destructive",size:"sm",onClick:()=>N(e.id,"Visitor log"),children:(0,a.jsx)(v.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("p",{className:"text-muted-foreground mb-1",children:"User Agent:"}),(0,a.jsx)("p",{className:"font-mono text-xs break-all",children:e.user_agent})]}),(0,a.jsx)("div",{className:"mt-2 text-xs text-muted-foreground",children:new Date(e.created_at).toLocaleString()})]},e.id))})})]})})]})]})}},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var a=s(2596),r=s(9688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[671,455,723,736,441,684,358],()=>t(5490)),_N_E=e.O()}]);