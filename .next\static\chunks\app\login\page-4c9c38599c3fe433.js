(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{285:(e,t,a)=>{"use strict";a.d(t,{$:()=>o});var r=a(5155);a(2115);var s=a(9708),i=a(2085),l=a(9434);let n=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:i,asChild:o=!1,...d}=e,c=o?s.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,l.cn)(n({variant:a,size:i,className:t})),...d})}},968:(e,t,a)=>{"use strict";a.d(t,{b:()=>n});var r=a(2115),s=a(3655),i=a(5155),l=r.forwardRef((e,t)=>(0,i.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var n=l},1007:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2523:(e,t,a)=>{"use strict";a.d(t,{p:()=>i});var r=a(5155);a(2115);var s=a(9434);function i(e){let{className:t,type:a,...i}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},2643:(e,t,a)=>{"use strict";a.d(t,{U:()=>s});var r=a(1935);function s(){let e="https://aovrwjzhqrgbdhszdowg.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFvdnJ3anpocXJnYmRoc3pkb3dnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4NTI5MjEsImV4cCI6MjA2NDQyODkyMX0.9TWCiuDturnqdOSlDeOWVroegkTM7Nra-W2LUoyGDSs";if(!e||!t)throw Error("Missing Supabase environment variables. Please check your .env.local file.");return(0,r.createBrowserClient)(e,t)}},2657:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2919:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},3655:(e,t,a)=>{"use strict";a.d(t,{hO:()=>o,sG:()=>n});var r=a(2115),s=a(7650),i=a(9708),l=a(5155),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let a=(0,i.TL)(`Primitive.${t}`),s=r.forwardRef((e,r)=>{let{asChild:s,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(s?a:t,{...i,ref:r})});return s.displayName=`Primitive.${t}`,{...e,[t]:s}},{});function o(e,t){e&&s.flushSync(()=>e.dispatchEvent(t))}},5057:(e,t,a)=>{"use strict";a.d(t,{J:()=>l});var r=a(5155);a(2115);var s=a(968),i=a(9434);function l(e){let{className:t,...a}=e;return(0,r.jsx)(s.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},5525:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5695:(e,t,a)=>{"use strict";var r=a(8999);a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},6159:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>N});var r=a(5155),s=a(2115),i=a(6695),l=a(285),n=a(2523),o=a(5057),d=a(2643),c=a(6671),u=a(5695),m=a(6874),h=a.n(m),x=a(9946);let p=(0,x.A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var f=a(8883),v=a(2919),g=a(8749),b=a(2657);let y=(0,x.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var w=a(5525),j=a(1007);function N(){let[e,t]=(0,s.useState)(!0),[a,m]=(0,s.useState)({email:"",password:"",fullName:""}),[x,N]=(0,s.useState)(!1),[k,A]=(0,s.useState)(!1),C=(0,d.U)(),S=(0,u.useRouter)(),z=async r=>{r.preventDefault(),N(!0);try{if(e){let{data:e,error:t}=await C.auth.signInWithPassword({email:a.email,password:a.password});if(t)throw t;if(e.user){let{data:t}=await C.from("profiles").select("role, full_name").eq("id",e.user.id).single();c.oR.success("Welcome back".concat((null==t?void 0:t.full_name)?", ".concat(t.full_name):"","!")),(null==t?void 0:t.role)==="admin"?S.push("/admin"):S.push("/dashboard")}}else{let{data:e,error:r}=await C.auth.signUp({email:a.email,password:a.password,options:{data:{full_name:a.fullName}}});if(r)throw r;if(e.user){let{error:r}=await C.from("profiles").insert({id:e.user.id,full_name:a.fullName,role:"user"});r&&console.error("Profile creation error:",r),c.oR.success("Account created successfully! Please check your email to verify your account."),t(!0)}}}catch(e){console.error("Auth error:",e),c.oR.error(e.message||"Authentication failed")}finally{N(!1)}},M=async()=>{try{let{error:e}=await C.auth.signInWithOAuth({provider:"google",options:{redirectTo:"".concat(window.location.origin,"/auth/callback")}});if(e)throw e}catch(e){console.error("Google auth error:",e),c.oR.error("Google authentication failed")}},I=async e=>{let t={admin:{email:"<EMAIL>",password:"admin123"},user:{email:"<EMAIL>",password:"user123"}};m({...a,email:t[e].email,password:t[e].password}),setTimeout(()=>{let e=document.querySelector("form");e&&e.requestSubmit()},100)},P=e=>{m(t=>({...t,[e.target.name]:e.target.value}))};return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4",children:(0,r.jsxs)(i.Zp,{className:"w-full max-w-md shadow-xl",children:[(0,r.jsxs)(i.aR,{className:"text-center",children:[(0,r.jsx)(i.ZB,{className:"text-2xl font-bold",children:e?"Welcome Back":"Create Account"}),(0,r.jsx)(i.BT,{children:e?"Sign in to your account to continue":"Create a new account to get started"})]}),(0,r.jsxs)(i.Wu,{children:[(0,r.jsxs)("form",{onSubmit:z,className:"space-y-4",children:[!e&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"fullName",children:"Full Name"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(n.p,{id:"fullName",name:"fullName",placeholder:"Enter your full name",value:a.fullName,onChange:P,required:!e,className:"pl-10"}),(0,r.jsx)(p,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"email",children:"Email"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(n.p,{id:"email",name:"email",type:"email",placeholder:"Enter your email",value:a.email,onChange:P,required:!0,className:"pl-10"}),(0,r.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"password",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(n.p,{id:"password",name:"password",type:k?"text":"password",placeholder:"Enter your password",value:a.password,onChange:P,required:!0,className:"pl-10 pr-10"}),(0,r.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,r.jsx)("button",{type:"button",onClick:()=>A(!k),className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:k?(0,r.jsx)(g.A,{className:"h-4 w-4 text-muted-foreground"}):(0,r.jsx)(b.A,{className:"h-4 w-4 text-muted-foreground"})})]})]}),!e&&(0,r.jsx)("div",{className:"text-right",children:(0,r.jsx)(h(),{href:"/forgot-password",className:"text-sm text-blue-600 hover:text-blue-800",children:"Forgot password?"})}),(0,r.jsx)(l.$,{type:"submit",className:"w-full",disabled:x,children:x?(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),e?"Signing in...":"Creating account..."]}):(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[e?"Sign In":"Sign Up",(0,r.jsx)(y,{className:"h-4 w-4"})]})})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("span",{className:"w-full border-t"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,r.jsx)("span",{className:"bg-background px-2 text-muted-foreground",children:"Or continue with"})})]}),(0,r.jsxs)(l.$,{variant:"outline",onClick:M,className:"w-full mt-4",disabled:x,children:[(0,r.jsxs)("svg",{className:"w-4 h-4 mr-2",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,r.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,r.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,r.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Continue with Google"]})]}),e&&(0,r.jsxs)("div",{className:"mt-6 p-4 bg-muted rounded-lg",children:[(0,r.jsx)("h4",{className:"text-sm font-semibold mb-3 text-center",children:"Quick Demo Access"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,r.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>I("admin"),disabled:x,className:"flex items-center gap-2",children:[(0,r.jsx)(w.A,{className:"h-3 w-3"}),"Admin Demo"]}),(0,r.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>I("user"),disabled:x,className:"flex items-center gap-2",children:[(0,r.jsx)(j.A,{className:"h-3 w-3"}),"User Demo"]})]}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground mt-2 text-center",children:"Click to auto-fill demo credentials"})]}),(0,r.jsx)("div",{className:"text-center mt-6",children:(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e?"Don't have an account?":"Already have an account?"," ",(0,r.jsx)("button",{type:"button",onClick:()=>t(!e),className:"text-blue-600 hover:text-blue-800 font-medium",children:e?"Sign up":"Sign in"})]})})]})]})})}},6695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>n,Zp:()=>i,aR:()=>l});var r=a(5155);a(2115);var s=a(9434);function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}},8609:(e,t,a)=>{Promise.resolve().then(a.bind(a,6159))},8749:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},8883:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>i});var r=a(2596),s=a(9688);function i(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[671,935,455,874,441,684,358],()=>t(8609)),_N_E=e.O()}]);