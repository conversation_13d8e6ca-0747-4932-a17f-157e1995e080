"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { createClient } from "@/lib/supabase/client"
import { Database } from "@/lib/database.types"
import { toast } from "sonner"
import { useRouter } from "next/navigation"
import {
  Mail,
  ShoppingBag,
  Settings,
  Eye,
  Download,
  Trash2,
  ArrowUpDown,
  Users,
  FileText,
  Plus,
  Edit,
  ExternalLink,
  IndianRupee,
  Tag,
  Code,
  Link,
  Image,
  Star,
  Crown,
  Gift,
  X,
  Save,
  Loader2
} from "lucide-react"

type ContactRequest = Database['public']['Tables']['contact_requests']['Row']
type Purchase = Database['public']['Tables']['purchases']['Row'] & {
  templates: Database['public']['Tables']['templates']['Row']
  profiles: Database['public']['Tables']['profiles']['Row']
}
type Customization = Database['public']['Tables']['customizations']['Row'] & {
  profiles: Database['public']['Tables']['profiles']['Row']
}
type VisitorLog = Database['public']['Tables']['visitor_logs']['Row']
type Template = Database['public']['Tables']['templates']['Row']

export default function AdminPage() {
  const [user, setUser] = useState<any>(null)
  const [isAdmin, setIsAdmin] = useState(false)
  const [loading, setLoading] = useState(true)
  const [contactRequests, setContactRequests] = useState<ContactRequest[]>([])
  const [purchases, setPurchases] = useState<Purchase[]>([])
  const [customizations, setCustomizations] = useState<Customization[]>([])
  const [visitorLogs, setVisitorLogs] = useState<VisitorLog[]>([])
  const [templates, setTemplates] = useState<Template[]>([])
  const [activeTab, setActiveTab] = useState("templates")
  const [tabDataLoaded, setTabDataLoaded] = useState<Record<string, boolean>>({})

  const supabase = createClient()
  const router = useRouter()

  useEffect(() => {
    checkAdminAccess()
  }, [])

  useEffect(() => {
    if (isAdmin && activeTab && !tabDataLoaded[activeTab]) {
      loadTabData(activeTab)
    }
  }, [isAdmin, activeTab, tabDataLoaded])

  const checkAdminAccess = async () => {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser()

      if (userError || !user) {
        router.push('/')
        return
      }

      setUser(user)

      // Check if user is admin
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single()

      if (profileError || !profile || profile.role !== 'admin') {
        router.push('/')
        return
      }

      setIsAdmin(true)
    } catch (error) {
      console.error('Error checking admin access:', error)
      router.push('/')
    } finally {
      setLoading(false)
    }
  }

  const loadTabData = async (tab: string) => {
    try {
      switch (tab) {
        case 'templates':
          await loadTemplates()
          break
        case 'contacts':
          await loadContactRequests()
          break
        case 'purchases':
          await loadPurchases()
          break
        case 'customizations':
          await loadCustomizations()
          break
        case 'visitors':
          await loadVisitorLogs()
          break
      }
      setTabDataLoaded(prev => ({ ...prev, [tab]: true }))
    } catch (error) {
      console.error(`Error loading ${tab} data:`, error)
      toast.error(`Failed to load ${tab} data`)
    }
  }

  const loadContactRequests = async () => {
    const { data, error } = await supabase
      .from('contact_requests')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) throw error
    setContactRequests(data || [])
  }

  const loadPurchases = async () => {
    try {
      console.log('Loading purchases...')
      const { data, error } = await supabase
        .from('purchases')
        .select(`
          *,
          templates (*),
          profiles (*)
        `)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Purchases query error:', error)
        // Try simpler query if join fails
        const { data: simpleData, error: simpleError } = await supabase
          .from('purchases')
          .select('*')
          .order('created_at', { ascending: false })

        if (simpleError) throw simpleError
        console.log('Purchases loaded (simple):', simpleData?.length || 0)
        setPurchases(simpleData || [])
        return
      }

      console.log('Purchases loaded:', data?.length || 0)
      setPurchases(data || [])
    } catch (error) {
      console.error('Error loading purchases:', error)
      toast.error('Failed to load purchases')
    }
  }

  const loadCustomizations = async () => {
    try {
      console.log('Loading customizations...')
      const { data, error } = await supabase
        .from('customizations')
        .select(`
          *,
          profiles (*)
        `)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Customizations query error:', error)
        // Try simpler query if join fails
        const { data: simpleData, error: simpleError } = await supabase
          .from('customizations')
          .select('*')
          .order('created_at', { ascending: false })

        if (simpleError) throw simpleError
        console.log('Customizations loaded (simple):', simpleData?.length || 0)
        setCustomizations(simpleData || [])
        return
      }

      console.log('Customizations loaded:', data?.length || 0)
      setCustomizations(data || [])
    } catch (error) {
      console.error('Error loading customizations:', error)
      toast.error('Failed to load customizations')
    }
  }

  const loadVisitorLogs = async () => {
    try {
      console.log('Loading visitor logs...')
      const { data, error } = await supabase
        .from('visitor_logs')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(1000) // Limit to recent 1000 logs

      if (error) {
        console.error('Visitor logs query error:', error)
        throw error
      }

      console.log('Visitor logs loaded:', data?.length || 0)
      setVisitorLogs(data || [])
    } catch (error) {
      console.error('Error loading visitor logs:', error)
      toast.error('Failed to load visitor logs')
    }
  }

  const loadTemplates = async () => {
    try {
      console.log('Loading templates...')
      const { data, error } = await supabase
        .from('templates')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Templates query error:', error)
        throw error
      }

      console.log('Templates loaded:', data?.length || 0)
      setTemplates(data || [])
    } catch (error) {
      console.error('Error loading templates:', error)
      toast.error('Failed to load templates')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Loading Admin Panel...</h1>
          <p className="text-muted-foreground">Verifying admin access</p>
        </div>
      </div>
    )
  }

  if (!isAdmin) {
    return null // Will redirect to home
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Admin Panel</h1>
        <p className="text-muted-foreground">
          Manage your application data and analytics
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Templates</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{templates.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Contact Requests</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{contactRequests.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Purchases</CardTitle>
            <ShoppingBag className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{purchases.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customizations</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{customizations.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Visitor Logs</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{visitorLogs.length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Admin Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="contacts">Contact Requests</TabsTrigger>
          <TabsTrigger value="purchases">Purchases</TabsTrigger>
          <TabsTrigger value="customizations">Customizations</TabsTrigger>
          <TabsTrigger value="visitors">Visitor Logs</TabsTrigger>
        </TabsList>

        <TabsContent value="templates" className="space-y-4">
          {tabDataLoaded.templates ? (
            <TemplatesTab
              data={templates}
              onRefresh={() => {
                setTabDataLoaded(prev => ({ ...prev, templates: false }))
                loadTemplates()
              }}
            />
          ) : (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-muted-foreground">Loading templates...</p>
              </div>
            </div>
          )}
        </TabsContent>

        <TabsContent value="contacts" className="space-y-4">
          {tabDataLoaded.contacts ? (
            <ContactRequestsTab
              data={contactRequests}
              onRefresh={() => {
                setTabDataLoaded(prev => ({ ...prev, contacts: false }))
                loadContactRequests()
              }}
            />
          ) : (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-muted-foreground">Loading contact requests...</p>
              </div>
            </div>
          )}
        </TabsContent>

        <TabsContent value="purchases" className="space-y-4">
          {tabDataLoaded.purchases ? (
            <PurchasesTab
              data={purchases}
              onRefresh={() => {
                setTabDataLoaded(prev => ({ ...prev, purchases: false }))
                loadPurchases()
              }}
            />
          ) : (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-muted-foreground">Loading purchases...</p>
              </div>
            </div>
          )}
        </TabsContent>

        <TabsContent value="customizations" className="space-y-4">
          {tabDataLoaded.customizations ? (
            <CustomizationsTab
              data={customizations}
              onRefresh={() => {
                setTabDataLoaded(prev => ({ ...prev, customizations: false }))
                loadCustomizations()
              }}
            />
          ) : (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-muted-foreground">Loading customizations...</p>
              </div>
            </div>
          )}
        </TabsContent>

        <TabsContent value="visitors" className="space-y-4">
          {tabDataLoaded.visitors ? (
            <VisitorLogsTab
              data={visitorLogs}
              onRefresh={() => {
                setTabDataLoaded(prev => ({ ...prev, visitors: false }))
                loadVisitorLogs()
              }}
            />
          ) : (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-muted-foreground">Loading visitor logs...</p>
              </div>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}

// Contact Requests Tab Component
function ContactRequestsTab({ data, onRefresh }: { data: ContactRequest[], onRefresh: () => void }) {
  const supabase = createClient()
  const [sortField, setSortField] = useState<keyof ContactRequest>('created_at')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  const sortedData = [...data].sort((a, b) => {
    const aVal = a[sortField]
    const bVal = b[sortField]

    if (aVal < bVal) return sortDirection === 'asc' ? -1 : 1
    if (aVal > bVal) return sortDirection === 'asc' ? 1 : -1
    return 0
  })

  const handleSort = (field: keyof ContactRequest) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this contact request?')) return

    try {
      const { error } = await supabase
        .from('contact_requests')
        .delete()
        .eq('id', id)

      if (error) throw error

      toast.success('Contact request deleted successfully')
      onRefresh()
    } catch (error) {
      console.error('Error deleting contact request:', error)
      toast.error('Failed to delete contact request')
    }
  }

  const exportToCSV = () => {
    const headers = ['Name', 'Email', 'Message', 'Created At']
    const csvData = [
      headers,
      ...sortedData.map(item => [
        item.name,
        item.email,
        item.message,
        new Date(item.created_at).toLocaleString()
      ])
    ]

    const csvContent = csvData.map(row =>
      row.map(field => `"${field}"`).join(',')
    ).join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `contact-requests-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Contact Requests</CardTitle>
            <CardDescription>Manage customer inquiries and messages</CardDescription>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={exportToCSV}>
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {sortedData.map((request) => (
            <div key={request.id} className="border rounded-lg p-4">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-semibold">{request.name}</h4>
                  <p className="text-sm text-muted-foreground">{request.email}</p>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSort('created_at')}
                  >
                    <ArrowUpDown className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDelete(request.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <p className="text-sm mb-2">{request.message}</p>
              <p className="text-xs text-muted-foreground">
                {new Date(request.created_at).toLocaleString()}
              </p>
            </div>
          ))}
          {sortedData.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              No contact requests found
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// Purchases Tab Component
function PurchasesTab({ data, onRefresh }: { data: Purchase[], onRefresh: () => void }) {
  const supabase = createClient()
  const [sortField, setSortField] = useState<keyof Purchase>('created_at')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  const sortedData = [...data].sort((a, b) => {
    const aVal = a[sortField]
    const bVal = b[sortField]

    if (aVal < bVal) return sortDirection === 'asc' ? -1 : 1
    if (aVal > bVal) return sortDirection === 'asc' ? 1 : -1
    return 0
  })

  const handleSort = (field: keyof Purchase) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this purchase record?')) return

    try {
      const { error } = await supabase
        .from('purchases')
        .delete()
        .eq('id', id)

      if (error) throw error

      toast.success('Purchase record deleted successfully')
      onRefresh()
    } catch (error) {
      console.error('Error deleting purchase:', error)
      toast.error('Failed to delete purchase record')
    }
  }

  const exportToCSV = () => {
    const headers = ['User Email', 'Template', 'Amount', 'Currency', 'Status', 'Payment ID', 'Created At']
    const csvData = [
      headers,
      ...sortedData.map(item => [
        item.profiles?.full_name || 'Unknown',
        item.templates?.title || 'Unknown Template',
        item.amount.toString(),
        item.currency,
        item.status,
        item.razorpay_payment_id || item.payment_id || 'N/A',
        new Date(item.created_at).toLocaleString()
      ])
    ]

    const csvContent = csvData.map(row =>
      row.map(field => `"${field}"`).join(',')
    ).join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `purchases-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  const totalRevenue = sortedData.reduce((sum, purchase) => sum + purchase.amount, 0)

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Purchases</CardTitle>
            <CardDescription>
              Total Revenue: ₹{totalRevenue} • {sortedData.length} purchases
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={exportToCSV}>
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {sortedData.map((purchase) => (
            <div key={purchase.id} className="border rounded-lg p-4">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-semibold">{purchase.templates?.title}</h4>
                  <p className="text-sm text-muted-foreground">
                    {purchase.profiles?.full_name || 'Unknown User'}
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSort('amount')}
                  >
                    <ArrowUpDown className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDelete(purchase.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Amount:</span>
                  <p className="font-medium">₹{purchase.amount}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">Status:</span>
                  <Badge variant={purchase.status === 'completed' || purchase.status === 'active' ? 'default' : 'secondary'}>
                    {purchase.status}
                  </Badge>
                </div>
                <div>
                  <span className="text-muted-foreground">Payment ID:</span>
                  <p className="font-mono text-xs">{purchase.razorpay_payment_id || purchase.payment_id || 'N/A'}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">Date:</span>
                  <p>{new Date(purchase.created_at).toLocaleDateString()}</p>
                </div>
              </div>
            </div>
          ))}
          {sortedData.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <p>No purchases found</p>
              <p className="text-sm mt-2">
                If you have purchase data in your database but it's not showing here,
                there might be a schema mismatch. Check the browser console for errors.
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// Customizations Tab Component
function CustomizationsTab({ data, onRefresh }: { data: Customization[], onRefresh: () => void }) {
  const supabase = createClient()
  const [sortField, setSortField] = useState<keyof Customization>('created_at')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  const sortedData = [...data].sort((a, b) => {
    const aVal = a[sortField]
    const bVal = b[sortField]

    if (aVal < bVal) return sortDirection === 'asc' ? -1 : 1
    if (aVal > bVal) return sortDirection === 'asc' ? 1 : -1
    return 0
  })

  const handleSort = (field: keyof Customization) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this customization?')) return

    try {
      const { error } = await supabase
        .from('customizations')
        .delete()
        .eq('id', id)

      if (error) throw error

      toast.success('Customization deleted successfully')
      onRefresh()
    } catch (error) {
      console.error('Error deleting customization:', error)
      toast.error('Failed to delete customization')
    }
  }

  const exportToCSV = () => {
    const headers = ['User', 'Navbar Style', 'Hero Section', 'Footer Style', 'Created At', 'Updated At']
    const csvData = [
      headers,
      ...sortedData.map(item => [
        item.profiles?.full_name || 'Unknown',
        item.navbar_style,
        item.hero_section,
        item.footer_style,
        new Date(item.created_at).toLocaleString(),
        new Date(item.updated_at).toLocaleString()
      ])
    ]

    const csvContent = csvData.map(row =>
      row.map(field => `"${field}"`).join(',')
    ).join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `customizations-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Customizations</CardTitle>
            <CardDescription>User template customization sessions</CardDescription>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={exportToCSV}>
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {sortedData.map((customization) => (
            <div key={customization.id} className="border rounded-lg p-4">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-semibold">
                    {customization.profiles?.full_name || 'Unknown User'}
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    ID: {customization.id.slice(0, 8)}...
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSort('created_at')}
                  >
                    <ArrowUpDown className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDelete(customization.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Navbar:</span>
                  <p className="font-medium">{customization.navbar_style}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">Hero:</span>
                  <p className="font-medium">{customization.hero_section}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">Footer:</span>
                  <p className="font-medium">{customization.footer_style}</p>
                </div>
              </div>
              <div className="mt-2 text-xs text-muted-foreground">
                Created: {new Date(customization.created_at).toLocaleString()} •
                Updated: {new Date(customization.updated_at).toLocaleString()}
              </div>
            </div>
          ))}
          {sortedData.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <p>No customizations found</p>
              <p className="text-sm mt-2">
                If you have customization data in your database but it's not showing here,
                check the browser console for errors or visit /admin-debug for diagnostics.
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// Visitor Logs Tab Component
function VisitorLogsTab({ data, onRefresh }: { data: VisitorLog[], onRefresh: () => void }) {
  const supabase = createClient()
  const [sortField, setSortField] = useState<keyof VisitorLog>('created_at')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  const sortedData = [...data].sort((a, b) => {
    const aVal = a[sortField]
    const bVal = b[sortField]

    if (aVal < bVal) return sortDirection === 'asc' ? -1 : 1
    if (aVal > bVal) return sortDirection === 'asc' ? 1 : -1
    return 0
  })

  const handleSort = (field: keyof VisitorLog) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this visitor log?')) return

    try {
      const { error } = await supabase
        .from('visitor_logs')
        .delete()
        .eq('id', id)

      if (error) throw error

      toast.success('Visitor log deleted successfully')
      onRefresh()
    } catch (error) {
      console.error('Error deleting visitor log:', error)
      toast.error('Failed to delete visitor log')
    }
  }

  const exportToCSV = () => {
    const headers = ['IP Address', 'Path', 'User Agent', 'Created At']
    const csvData = [
      headers,
      ...sortedData.map(item => [
        item.ip_address || 'Unknown',
        item.path,
        item.user_agent || 'Unknown',
        new Date(item.created_at).toLocaleString()
      ])
    ]

    const csvContent = csvData.map(row =>
      row.map(field => `"${field}"`).join(',')
    ).join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `visitor-logs-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  const clearOldLogs = async () => {
    if (!confirm('Are you sure you want to delete logs older than 30 days?')) return

    try {
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const { error } = await supabase
        .from('visitor_logs')
        .delete()
        .lt('created_at', thirtyDaysAgo.toISOString())

      if (error) throw error

      toast.success('Old visitor logs cleared successfully')
      onRefresh()
    } catch (error) {
      console.error('Error clearing old logs:', error)
      toast.error('Failed to clear old logs')
    }
  }

  // Analytics
  const uniqueIPs = new Set(sortedData.map(log => log.ip_address)).size
  const topPaths = sortedData.reduce((acc, log) => {
    acc[log.path] = (acc[log.path] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const topPathsArray = Object.entries(topPaths)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5)

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Visitor Logs</CardTitle>
            <CardDescription>
              {sortedData.length} visits • {uniqueIPs} unique IPs
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={clearOldLogs}>
              <Trash2 className="h-4 w-4 mr-2" />
              Clear Old
            </Button>
            <Button variant="outline" onClick={exportToCSV}>
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Top Paths Analytics */}
        <div className="mb-6 p-4 bg-muted rounded-lg">
          <h4 className="font-semibold mb-2">Top Visited Pages</h4>
          <div className="space-y-1">
            {topPathsArray.map(([path, count]) => (
              <div key={path} className="flex justify-between text-sm">
                <span>{path}</span>
                <span className="font-medium">{count} visits</span>
              </div>
            ))}
          </div>
        </div>

        <div className="space-y-4">
          {sortedData.map((log) => (
            <div key={log.id} className="border rounded-lg p-4">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-semibold">{log.path}</h4>
                  <p className="text-sm text-muted-foreground">
                    IP: {log.ip_address || 'Unknown'}
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSort('created_at')}
                  >
                    <ArrowUpDown className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDelete(log.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="text-sm">
                <p className="text-muted-foreground mb-1">User Agent:</p>
                <p className="font-mono text-xs break-all">
                  {log.user_agent || 'Unknown'}
                </p>
              </div>
              <div className="mt-2 text-xs text-muted-foreground">
                {new Date(log.created_at).toLocaleString()}
              </div>
            </div>
          ))}
          {sortedData.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <p>No visitor logs found</p>
              <p className="text-sm mt-2">
                If you have visitor log data in your database but it's not showing here,
                check the browser console for errors or visit /admin-debug for diagnostics.
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// Templates Tab Component
function TemplatesTab({ data, onRefresh }: { data: Template[], onRefresh: () => void }) {
  const [sortBy, setSortBy] = useState<'title' | 'price' | 'category' | 'created_at'>('created_at')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [editingTemplate, setEditingTemplate] = useState<Template | null>(null)
  const supabase = createClient()

  const sortedData = [...data].sort((a, b) => {
    const aVal = a[sortBy]
    const bVal = b[sortBy]

    if (sortBy === 'price') {
      return sortOrder === 'asc' ? aVal - bVal : bVal - aVal
    }

    const comparison = String(aVal).localeCompare(String(bVal))
    return sortOrder === 'asc' ? comparison : -comparison
  })

  const handleSort = (field: typeof sortBy) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('asc')
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this template?')) return

    try {
      const { error } = await supabase
        .from('templates')
        .delete()
        .eq('id', id)

      if (error) throw error

      toast.success('Template deleted successfully')
      onRefresh()
    } catch (error) {
      console.error('Error deleting template:', error)
      toast.error('Failed to delete template')
    }
  }

  const exportToCSV = () => {
    const headers = ['Title', 'Description', 'Price', 'Category', 'Preview URL', 'Created At']
    const csvData = [
      headers,
      ...sortedData.map(template => [
        template.title,
        template.description,
        template.price.toString(),
        template.category,
        template.preview_url || '',
        new Date(template.created_at).toLocaleDateString()
      ])
    ]

    const csvContent = csvData.map(row =>
      row.map(field => `"${field}"`).join(',')
    ).join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `templates-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Templates Management</CardTitle>
            <CardDescription>
              Manage your template collection
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button onClick={() => setShowAddDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Template
            </Button>
            <Button variant="outline" onClick={exportToCSV}>
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {sortedData.map((template) => (
            <div key={template.id} className="border rounded-lg p-4">
              <div className="flex justify-between items-start mb-2">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-semibold text-lg">{template.title}</h4>
                    <Badge variant="secondary">{template.category}</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    {template.description}
                  </p>
                  <div className="flex items-center gap-4 text-sm">
                    <span className="font-medium text-green-600">
                      ₹{template.price}
                    </span>
                    {template.preview_url && (
                      <a
                        href={template.preview_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 flex items-center gap-1"
                      >
                        <ExternalLink className="h-3 w-3" />
                        Preview Link
                      </a>
                    )}
                    <span className="text-muted-foreground">
                      Created: {new Date(template.created_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setEditingTemplate(template)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDelete(template.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
          {sortedData.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              No templates found
            </div>
          )}
        </div>
      </CardContent>

      {/* Add/Edit Template Dialog */}
      <TemplateDialog
        open={showAddDialog || !!editingTemplate}
        onClose={() => {
          setShowAddDialog(false)
          setEditingTemplate(null)
        }}
        template={editingTemplate}
        onSuccess={() => {
          setShowAddDialog(false)
          setEditingTemplate(null)
          onRefresh()
        }}
      />
    </Card>
  )
}

// Template Dialog Component
function TemplateDialog({
  open,
  onClose,
  template,
  onSuccess
}: {
  open: boolean
  onClose: () => void
  template: Template | null
  onSuccess: () => void
}) {
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    description: '',
    long_description: '',
    price: '',
    original_price: '',
    discount_percentage: '',
    category_id: '',
    preview_image: '',
    preview_url: '',
    demo_url: '',
    download_url: '',
    version: '1.0.0',
    features: '',
    tech_stack: '',
    difficulty_level: 'beginner',
    estimated_time: '',
    license_type: 'standard',
    is_featured: false,
    is_premium: false,
    is_free: false
  })

  // Auto-calculate discount percentage when prices change
  useEffect(() => {
    const currentPrice = parseFloat(formData.price) || 0
    const originalPrice = parseFloat(formData.original_price) || 0

    if (originalPrice > 0 && currentPrice > 0 && originalPrice > currentPrice) {
      const discountPercentage = Math.round(((originalPrice - currentPrice) / originalPrice) * 100)
      setFormData(prev => ({ ...prev, discount_percentage: discountPercentage.toString() }))
    } else if (originalPrice <= currentPrice) {
      setFormData(prev => ({ ...prev, discount_percentage: '' }))
    }
  }, [formData.price, formData.original_price])

  // Auto-generate slug from title
  useEffect(() => {
    if (formData.title && !template) {
      const slug = formData.title.toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '')
      setFormData(prev => ({ ...prev, slug }))
    }
  }, [formData.title, template])
  const [loading, setLoading] = useState(false)
  const [categories, setCategories] = useState<any[]>([])
  const supabase = createClient()

  useEffect(() => {
    loadCategories()
  }, [])

  useEffect(() => {
    if (template) {
      setFormData({
        title: template.title || '',
        slug: template.slug || '',
        description: template.description || '',
        long_description: template.long_description || '',
        price: template.price?.toString() || '',
        original_price: template.original_price?.toString() || '',
        discount_percentage: template.discount_percentage?.toString() || '',
        category_id: template.category_id || '',
        preview_image: template.preview_image || '',
        preview_url: template.preview_url || '',
        demo_url: template.demo_url || '',
        download_url: template.download_url || '',
        version: template.version || '1.0.0',
        features: Array.isArray(template.features) ? template.features.join(', ') : '',
        tech_stack: Array.isArray(template.tech_stack) ? template.tech_stack.join(', ') : '',
        difficulty_level: template.difficulty_level || 'beginner',
        estimated_time: template.estimated_time || '',
        license_type: template.license_type || 'standard',
        is_featured: template.is_featured || false,
        is_premium: template.is_premium || false,
        is_free: template.is_free || false
      })
    } else {
      setFormData({
        title: '',
        slug: '',
        description: '',
        long_description: '',
        price: '',
        original_price: '',
        discount_percentage: '',
        category_id: '',
        preview_image: '',
        preview_url: '',
        demo_url: '',
        download_url: '',
        version: '1.0.0',
        features: '',
        tech_stack: '',
        difficulty_level: 'beginner',
        estimated_time: '',
        license_type: 'standard',
        is_featured: false,
        is_premium: false,
        is_free: false
      })
    }
  }, [template])

  const loadCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .eq('is_active', true)
        .order('name')

      if (error) {
        console.log('Categories table not found, using default categories')
        // Fallback to default categories if table doesn't exist
        setCategories([
          { id: 'business', name: 'Business' },
          { id: 'portfolio', name: 'Portfolio' },
          { id: 'ecommerce', name: 'E-commerce' },
          { id: 'blog', name: 'Blog' },
          { id: 'marketing', name: 'Marketing' },
          { id: 'restaurant', name: 'Restaurant' }
        ])
        return
      }
      setCategories(data || [])
    } catch (error) {
      console.error('Error loading categories:', error)
      // Fallback categories
      setCategories([
        { id: 'business', name: 'Business' },
        { id: 'portfolio', name: 'Portfolio' },
        { id: 'ecommerce', name: 'E-commerce' },
        { id: 'blog', name: 'Blog' },
        { id: 'marketing', name: 'Marketing' },
        { id: 'restaurant', name: 'Restaurant' }
      ])
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Generate slug from title if not provided
      const slug = formData.slug || formData.title.toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '')

      // Create template data object, handling both old and new schema
      const baseTemplateData = {
        title: formData.title,
        description: formData.description,
        price: parseInt(formData.price) || 0,
        preview_image: formData.preview_image || null,
        preview_url: formData.preview_url || null
      }

      // Add enhanced fields if they exist in the schema
      const enhancedTemplateData = {
        ...baseTemplateData,
        slug: slug,
        long_description: formData.long_description || null,
        original_price: formData.original_price ? parseInt(formData.original_price) : null,
        discount_percentage: formData.discount_percentage ? parseInt(formData.discount_percentage) : 0,
        category_id: formData.category_id || null,
        demo_url: formData.demo_url || null,
        download_url: formData.download_url || null,
        version: formData.version || '1.0.0',
        features: formData.features ? formData.features.split(',').map(f => f.trim()).filter(f => f) : [],
        tech_stack: formData.tech_stack ? formData.tech_stack.split(',').map(t => t.trim()).filter(t => t) : [],
        difficulty_level: formData.difficulty_level,
        estimated_time: formData.estimated_time || null,
        license_type: formData.license_type,
        is_featured: formData.is_featured,
        is_premium: formData.is_premium,
        is_free: formData.is_free,
        is_active: true
      }

      // Try enhanced schema first, fallback to basic if it fails
      let templateData = enhancedTemplateData

      // If using old schema, add category as string instead of category_id
      if (!formData.category_id && categories.length > 0) {
        const selectedCategory = categories.find(c => c.id === formData.category_id)
        templateData = {
          ...baseTemplateData,
          category: selectedCategory?.name || 'Business'
        }
      }

      if (template) {
        // Update existing template
        const { error } = await supabase
          .from('templates')
          .update(templateData)
          .eq('id', template.id)

        if (error) {
          // If enhanced fields fail, try with basic fields only
          if (error.message.includes('column') && error.message.includes('does not exist')) {
            console.log('Falling back to basic template schema')
            const { error: basicError } = await supabase
              .from('templates')
              .update(baseTemplateData)
              .eq('id', template.id)

            if (basicError) throw basicError
          } else {
            throw error
          }
        }
        toast.success('Template updated successfully')
      } else {
        // Create new template
        const { error } = await supabase
          .from('templates')
          .insert(templateData)

        if (error) {
          // If enhanced fields fail, try with basic fields only
          if (error.message.includes('column') && error.message.includes('does not exist')) {
            console.log('Falling back to basic template schema')
            const { error: basicError } = await supabase
              .from('templates')
              .insert(baseTemplateData)

            if (basicError) throw basicError
          } else {
            throw error
          }
        }
        toast.success('Template created successfully')
      }

      onSuccess()
    } catch (error: any) {
      console.error('Error saving template:', error)
      toast.error(error.message || 'Failed to save template')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto">
        <DialogHeader className="pb-6 border-b">
          <DialogTitle className="text-2xl font-bold flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              {template ? (
                <Edit className="h-6 w-6 text-primary" />
              ) : (
                <Plus className="h-6 w-6 text-primary" />
              )}
            </div>
            {template ? 'Edit Template' : 'Add New Template'}
          </DialogTitle>
          <p className="text-muted-foreground mt-2">
            {template
              ? 'Update template information and settings'
              : 'Create a new template with all the necessary details and pricing information'
            }
          </p>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-8 py-6">
          {/* Basic Information */}
          <div className="space-y-6 p-6 bg-muted/30 rounded-xl border">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileText className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">Basic Information</h3>
                <p className="text-sm text-muted-foreground">Essential template details and descriptions</p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label htmlFor="title" className="text-sm font-medium">Template Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  required
                  className="h-12 text-base"
                  placeholder="Enter template title..."
                />
              </div>

              <div className="space-y-3">
                <Label htmlFor="slug" className="text-sm font-medium">URL Slug</Label>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={(e) => setFormData({ ...formData, slug: e.target.value })}
                  placeholder="auto-generated-from-title"
                  className="h-12 text-base"
                />
                <p className="text-xs text-muted-foreground">Auto-generated from title if left empty</p>
              </div>
            </div>

            <div className="space-y-3">
              <Label htmlFor="description" className="text-sm font-medium">Short Description *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                required
                rows={3}
                className="text-base resize-none"
                placeholder="Brief description for template cards and listings..."
              />
              <p className="text-xs text-muted-foreground">This appears on template cards (max 150 characters recommended)</p>
            </div>

            <div className="space-y-3">
              <Label htmlFor="long_description" className="text-sm font-medium">Detailed Description</Label>
              <Textarea
                id="long_description"
                value={formData.long_description}
                onChange={(e) => setFormData({ ...formData, long_description: e.target.value })}
                rows={5}
                className="text-base resize-none"
                placeholder="Comprehensive description including features, use cases, and benefits..."
              />
              <p className="text-xs text-muted-foreground">Detailed description for template detail pages</p>
            </div>
          </div>

          {/* Pricing */}
          <div className="space-y-6 p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl border border-green-200">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <IndianRupee className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">Pricing & Revenue</h3>
                <p className="text-sm text-muted-foreground">Set competitive pricing with automatic discount calculation</p>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-6">
              <div className="space-y-3">
                <Label htmlFor="price" className="text-sm font-medium flex items-center gap-2">
                  <span>Current Price (₹) *</span>
                  <Badge variant="secondary" className="text-xs">Selling Price</Badge>
                </Label>
                <Input
                  id="price"
                  type="number"
                  min="0"
                  step="1"
                  value={formData.price}
                  onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                  required
                  className="h-12 text-base font-medium"
                  placeholder="999"
                />
                <p className="text-xs text-muted-foreground">Final price customers will pay</p>
              </div>

              <div className="space-y-3">
                <Label htmlFor="original_price" className="text-sm font-medium flex items-center gap-2">
                  <span>Original Price (₹)</span>
                  <Badge variant="outline" className="text-xs">Optional</Badge>
                </Label>
                <Input
                  id="original_price"
                  type="number"
                  min="0"
                  step="1"
                  value={formData.original_price}
                  onChange={(e) => setFormData({ ...formData, original_price: e.target.value })}
                  placeholder="1499"
                  className="h-12 text-base"
                />
                <p className="text-xs text-muted-foreground">Higher price to show discount value</p>
              </div>

              <div className="space-y-3">
                <Label htmlFor="discount_percentage" className="text-sm font-medium flex items-center gap-2">
                  <span>Discount %</span>
                  <Badge variant="secondary" className="text-xs bg-green-100 text-green-700">Auto-calculated</Badge>
                </Label>
                <Input
                  id="discount_percentage"
                  type="number"
                  min="0"
                  max="100"
                  value={formData.discount_percentage}
                  onChange={(e) => setFormData({ ...formData, discount_percentage: e.target.value })}
                  className="h-12 text-base font-medium"
                  placeholder="33"
                  readOnly
                />
                <p className="text-xs text-muted-foreground">Automatically calculated from prices</p>
              </div>
            </div>

            {/* Pricing Preview */}
            {formData.price && (
              <div className="mt-4 p-4 bg-white rounded-lg border border-green-200">
                <h4 className="text-sm font-medium mb-2">Pricing Preview:</h4>
                <div className="flex items-center gap-4">
                  <div className="text-2xl font-bold text-green-600">₹{formData.price}</div>
                  {formData.original_price && parseFloat(formData.original_price) > parseFloat(formData.price) && (
                    <>
                      <div className="text-lg text-muted-foreground line-through">₹{formData.original_price}</div>
                      <Badge className="bg-red-100 text-red-700 hover:bg-red-100">
                        {formData.discount_percentage}% OFF
                      </Badge>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Category and Classification */}
          <div className="space-y-6 p-6 bg-muted/30 rounded-xl border">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Tag className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">Category & Classification</h3>
                <p className="text-sm text-muted-foreground">Organize and classify your template for better discovery</p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label htmlFor="category_id" className="text-sm font-medium">Template Category *</Label>
                <Select
                  value={formData.category_id}
                  onValueChange={(value) => setFormData({ ...formData, category_id: value })}
                >
                  <SelectTrigger className="h-12 text-base">
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">Choose the most relevant category</p>
              </div>

              <div className="space-y-3">
                <Label htmlFor="difficulty_level" className="text-sm font-medium">Difficulty Level</Label>
                <Select
                  value={formData.difficulty_level}
                  onValueChange={(value) => setFormData({ ...formData, difficulty_level: value })}
                >
                  <SelectTrigger className="h-12 text-base">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="beginner">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        Beginner
                      </div>
                    </SelectItem>
                    <SelectItem value="intermediate">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                        Intermediate
                      </div>
                    </SelectItem>
                    <SelectItem value="advanced">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                        Advanced
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">Implementation complexity level</p>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-6">
              <div className="space-y-3">
                <Label htmlFor="version" className="text-sm font-medium">Version</Label>
                <Input
                  id="version"
                  value={formData.version}
                  onChange={(e) => setFormData({ ...formData, version: e.target.value })}
                  placeholder="1.0.0"
                  className="h-12 text-base"
                />
                <p className="text-xs text-muted-foreground">Semantic version number</p>
              </div>

              <div className="space-y-3">
                <Label htmlFor="estimated_time" className="text-sm font-medium">Setup Time</Label>
                <Input
                  id="estimated_time"
                  value={formData.estimated_time}
                  onChange={(e) => setFormData({ ...formData, estimated_time: e.target.value })}
                  placeholder="2-3 hours"
                  className="h-12 text-base"
                />
                <p className="text-xs text-muted-foreground">Time to implement/customize</p>
              </div>

              <div className="space-y-3">
                <Label htmlFor="license_type" className="text-sm font-medium">License Type</Label>
                <Select
                  value={formData.license_type}
                  onValueChange={(value) => setFormData({ ...formData, license_type: value })}
                >
                  <SelectTrigger className="h-12 text-base">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="standard">Standard License</SelectItem>
                    <SelectItem value="extended">Extended License</SelectItem>
                    <SelectItem value="commercial">Commercial License</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">Usage rights and restrictions</p>
              </div>
            </div>
          </div>

          {/* Features and Tech Stack */}
          <div className="space-y-6 p-6 bg-muted/30 rounded-xl border">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Code className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">Features & Technology</h3>
                <p className="text-sm text-muted-foreground">Highlight key features and technical specifications</p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label htmlFor="features" className="text-sm font-medium">Key Features</Label>
                <Textarea
                  id="features"
                  value={formData.features}
                  onChange={(e) => setFormData({ ...formData, features: e.target.value })}
                  placeholder="Responsive Design, Dark Mode, SEO Optimized, Mobile First, Fast Loading"
                  rows={4}
                  className="text-base resize-none"
                />
                <p className="text-xs text-muted-foreground">Separate features with commas. These will be displayed as badges.</p>
              </div>

              <div className="space-y-3">
                <Label htmlFor="tech_stack" className="text-sm font-medium">Technology Stack</Label>
                <Textarea
                  id="tech_stack"
                  value={formData.tech_stack}
                  onChange={(e) => setFormData({ ...formData, tech_stack: e.target.value })}
                  placeholder="Next.js 15, TypeScript, Tailwind CSS, Framer Motion, Supabase"
                  rows={4}
                  className="text-base resize-none"
                />
                <p className="text-xs text-muted-foreground">List technologies used. Helps developers understand requirements.</p>
              </div>
            </div>

            {/* Feature Preview */}
            {formData.features && (
              <div className="mt-4 p-4 bg-white rounded-lg border">
                <h4 className="text-sm font-medium mb-2">Features Preview:</h4>
                <div className="flex flex-wrap gap-2">
                  {formData.features.split(',').map((feature, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {feature.trim()}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* URLs */}
          <div className="space-y-6 p-6 bg-muted/30 rounded-xl border">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Link className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">URLs & Media</h3>
                <p className="text-sm text-muted-foreground">Add preview images, demo links, and download resources</p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label htmlFor="preview_image" className="text-sm font-medium flex items-center gap-2">
                  <Image className="h-4 w-4" />
                  Preview Image URL *
                </Label>
                <Input
                  id="preview_image"
                  type="url"
                  value={formData.preview_image}
                  onChange={(e) => setFormData({ ...formData, preview_image: e.target.value })}
                  placeholder="https://example.com/template-preview.jpg"
                  className="h-12 text-base"
                />
                <p className="text-xs text-muted-foreground">High-quality image for template cards (recommended: 800x600px)</p>
              </div>

              <div className="space-y-3">
                <Label htmlFor="preview_url" className="text-sm font-medium flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  Live Preview URL
                </Label>
                <Input
                  id="preview_url"
                  type="url"
                  value={formData.preview_url}
                  onChange={(e) => setFormData({ ...formData, preview_url: e.target.value })}
                  placeholder="https://preview.example.com"
                  className="h-12 text-base"
                />
                <p className="text-xs text-muted-foreground">Working demo for customers to preview</p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label htmlFor="demo_url" className="text-sm font-medium flex items-center gap-2">
                  <ExternalLink className="h-4 w-4" />
                  Demo URL
                </Label>
                <Input
                  id="demo_url"
                  type="url"
                  value={formData.demo_url}
                  onChange={(e) => setFormData({ ...formData, demo_url: e.target.value })}
                  placeholder="https://demo.example.com"
                  className="h-12 text-base"
                />
                <p className="text-xs text-muted-foreground">Interactive demo or showcase</p>
              </div>

              <div className="space-y-3">
                <Label htmlFor="download_url" className="text-sm font-medium flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  Download URL
                </Label>
                <Input
                  id="download_url"
                  type="url"
                  value={formData.download_url}
                  onChange={(e) => setFormData({ ...formData, download_url: e.target.value })}
                  placeholder="https://files.example.com/template.zip"
                  className="h-12 text-base"
                />
                <p className="text-xs text-muted-foreground">Direct download link for purchased templates</p>
              </div>
            </div>

            {/* Image Preview */}
            {formData.preview_image && (
              <div className="mt-4 p-4 bg-white rounded-lg border">
                <h4 className="text-sm font-medium mb-2">Image Preview:</h4>
                <div className="w-32 h-24 bg-muted rounded-lg overflow-hidden">
                  <img
                    src={formData.preview_image}
                    alt="Preview"
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none'
                    }}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Template Flags */}
          <div className="space-y-6 p-6 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl border border-purple-200">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Star className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">Template Status & Visibility</h3>
                <p className="text-sm text-muted-foreground">Control how your template appears to customers</p>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-6">
              <div className="p-4 bg-white rounded-lg border border-purple-100 hover:border-purple-200 transition-colors">
                <div className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    id="is_featured"
                    checked={formData.is_featured}
                    onChange={(e) => setFormData({ ...formData, is_featured: e.target.checked })}
                    className="mt-1 h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                  />
                  <div className="flex-1">
                    <Label htmlFor="is_featured" className="text-sm font-medium cursor-pointer">
                      Featured Template
                    </Label>
                    <p className="text-xs text-muted-foreground mt-1">
                      Appears in featured sections and gets priority placement
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-white rounded-lg border border-purple-100 hover:border-purple-200 transition-colors">
                <div className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    id="is_premium"
                    checked={formData.is_premium}
                    onChange={(e) => setFormData({ ...formData, is_premium: e.target.checked })}
                    className="mt-1 h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                  />
                  <div className="flex-1">
                    <Label htmlFor="is_premium" className="text-sm font-medium cursor-pointer">
                      Premium Template
                    </Label>
                    <p className="text-xs text-muted-foreground mt-1">
                      High-quality template with advanced features
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-white rounded-lg border border-purple-100 hover:border-purple-200 transition-colors">
                <div className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    id="is_free"
                    checked={formData.is_free}
                    onChange={(e) => setFormData({ ...formData, is_free: e.target.checked })}
                    className="mt-1 h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                  />
                  <div className="flex-1">
                    <Label htmlFor="is_free" className="text-sm font-medium cursor-pointer">
                      Free Template
                    </Label>
                    <p className="text-xs text-muted-foreground mt-1">
                      Available for free download without payment
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Status Summary */}
            <div className="mt-4 p-4 bg-white rounded-lg border border-purple-200">
              <h4 className="text-sm font-medium mb-2">Template Status Summary:</h4>
              <div className="flex flex-wrap gap-2">
                {formData.is_featured && (
                  <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
                    <Star className="h-3 w-3 mr-1" />
                    Featured
                  </Badge>
                )}
                {formData.is_premium && (
                  <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-100">
                    <Crown className="h-3 w-3 mr-1" />
                    Premium
                  </Badge>
                )}
                {formData.is_free && (
                  <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                    <Gift className="h-3 w-3 mr-1" />
                    Free
                  </Badge>
                )}
                {!formData.is_featured && !formData.is_premium && !formData.is_free && (
                  <Badge variant="outline">Standard Template</Badge>
                )}
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex gap-4 pt-8 border-t-2 border-muted">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1 h-12 text-base font-medium"
              disabled={loading}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="flex-1 h-12 text-base font-medium bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : template ? (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Update Template
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Template
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
