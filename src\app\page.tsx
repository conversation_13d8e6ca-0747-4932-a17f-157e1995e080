"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  ArrowRight,
  Palette,
  FileText,
  Zap,
  Star,
  Users,
  Shield,
  Smartphone,
  Globe,
  Code,
  Layers,
  Rocket,
  Heart,
  CheckCircle,
  TrendingUp,
  Award,
  Clock,
  Mail,
  Phone,
  MapPin,
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  Github,
  Eye,
  ShoppingCart,
  Search,
  Filter,
  Grid,
  List,
  Bookmark,
  Share2,
  MessageCircle,
  ThumbsUp,
  Lightbulb,
  Infinity,
  Cpu,
  Database,
  Cloud,
  Lock,
  Gauge,
  Headphones,
  Play,
  ExternalLink,
  Download,
  ChevronRight,
  Sparkles,
  Target,
  BarChart3
} from "lucide-react"
import Link from "next/link"
import { createClient } from "@/lib/supabase/client"
import { Database } from "@/lib/database.types"

type Template = Database['public']['Tables']['templates']['Row']

export default function Home() {
  const [featuredTemplates, setFeaturedTemplates] = useState<Template[]>([])
  const [stats, setStats] = useState({
    templates: 50,
    users: 10000,
    downloads: 25000,
    rating: 4.9
  })
  const [loading, setLoading] = useState(true)
  const [email, setEmail] = useState("")

  useEffect(() => {
    loadFeaturedTemplates()
    loadStats()
  }, [])

  const loadFeaturedTemplates = async () => {
    try {
      const supabase = createClient()
      const { data, error } = await supabase
        .from('templates')
        .select('*')
        .eq('is_featured', true)
        .eq('is_active', true)
        .limit(6)
        .order('created_at', { ascending: false })

      if (error) throw error
      setFeaturedTemplates(data || [])
    } catch (error) {
      console.error('Error loading featured templates:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const supabase = createClient()

      // Get template count
      const { count: templateCount } = await supabase
        .from('templates')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true)

      // Get user count
      const { count: userCount } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })

      // Get download count (sum of all template download_count)
      const { data: downloadData } = await supabase
        .from('templates')
        .select('download_count')
        .eq('is_active', true)

      const totalDownloads = downloadData?.reduce((sum, template) => sum + (template.download_count || 0), 0) || 0

      setStats({
        templates: templateCount || 50,
        users: userCount || 10000,
        downloads: totalDownloads || 25000,
        rating: 4.9
      })
    } catch (error) {
      console.error('Error loading stats:', error)
    }
  }

  const handleNewsletterSignup = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!email) return

    try {
      const supabase = createClient()
      const { error } = await supabase
        .from('email_subscriptions')
        .insert({
          email,
          source: 'homepage_newsletter'
        })

      if (error) throw error

      setEmail("")
      // Show success message (you can add toast notification here)
      alert("Thank you for subscribing to our newsletter!")
    } catch (error) {
      console.error('Newsletter signup error:', error)
      alert("Error subscribing. Please try again.")
    }
  }

  return (
    <div className="space-y-16">
      {/* Enhanced Hero Section */}
      <div className="relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50 -z-10" />
        <div className="absolute top-0 left-1/4 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse" />
        <div className="absolute top-0 right-1/4 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000" />

        <div className="text-center space-y-8 py-20">
          <div className="space-y-6">
            <Badge variant="secondary" className="text-sm px-6 py-3 bg-gradient-to-r from-blue-100 to-purple-100 border-0 hover:scale-105 transition-transform">
              <Sparkles className="h-4 w-4 mr-2 text-blue-600" />
              New: {stats.templates}+ Premium Templates Available
            </Badge>

            <h1 className="text-6xl md:text-7xl lg:text-8xl font-bold tracking-tight">
              <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent">
                Welcome to
              </span>
              <br />
              <span className="bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 bg-clip-text text-transparent">
                KaleidoneX
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
              Create stunning, customizable templates with our powerful design tools.
              Build beautiful websites and applications with ease using our advanced customization engine.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <Link href="/templates">
              <Button size="lg" className="text-lg px-10 py-6 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                Browse Templates
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link href="/customize">
              <Button variant="outline" size="lg" className="text-lg px-10 py-6 border-2 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:scale-105">
                <Palette className="mr-2 h-5 w-5" />
                Start Customizing
              </Button>
            </Link>
            <Link href="#demo">
              <Button variant="ghost" size="lg" className="text-lg px-6 py-6 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300">
                <Play className="mr-2 h-5 w-5" />
                Watch Demo
              </Button>
            </Link>
          </div>

          {/* Enhanced Trust Indicators */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 pt-12 max-w-2xl mx-auto">
            <div className="flex flex-col items-center gap-2 p-4 rounded-lg bg-white/50 backdrop-blur-sm border border-white/20 hover:scale-105 transition-transform">
              <Star className="h-6 w-6 text-yellow-500 fill-current" />
              <span className="font-semibold text-lg">{stats.rating}/5</span>
              <span className="text-sm text-muted-foreground">Rating</span>
            </div>
            <div className="flex flex-col items-center gap-2 p-4 rounded-lg bg-white/50 backdrop-blur-sm border border-white/20 hover:scale-105 transition-transform">
              <Users className="h-6 w-6 text-blue-500" />
              <span className="font-semibold text-lg">{(stats.users / 1000).toFixed(0)}K+</span>
              <span className="text-sm text-muted-foreground">Users</span>
            </div>
            <div className="flex flex-col items-center gap-2 p-4 rounded-lg bg-white/50 backdrop-blur-sm border border-white/20 hover:scale-105 transition-transform">
              <Download className="h-6 w-6 text-green-500" />
              <span className="font-semibold text-lg">{(stats.downloads / 1000).toFixed(0)}K+</span>
              <span className="text-sm text-muted-foreground">Downloads</span>
            </div>
            <div className="flex flex-col items-center gap-2 p-4 rounded-lg bg-white/50 backdrop-blur-sm border border-white/20 hover:scale-105 transition-transform">
              <Shield className="h-6 w-6 text-purple-500" />
              <span className="font-semibold text-lg">100%</span>
              <span className="text-sm text-muted-foreground">Secure</span>
            </div>
          </div>
        </div>
      </div>

      {/* Featured Templates Section */}
      <div className="space-y-8">
        <div className="text-center space-y-4">
          <Badge variant="outline" className="px-4 py-2">
            <Award className="h-4 w-4 mr-2" />
            Featured Templates
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Popular Templates
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Discover our most popular and highly-rated templates, loved by thousands of users worldwide
          </p>
        </div>

        {loading ? (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="overflow-hidden animate-pulse">
                <div className="aspect-video bg-gray-200" />
                <CardContent className="p-6">
                  <div className="space-y-3">
                    <div className="h-4 bg-gray-200 rounded w-3/4" />
                    <div className="h-3 bg-gray-200 rounded w-full" />
                    <div className="h-3 bg-gray-200 rounded w-2/3" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {featuredTemplates.map((template) => (
              <Card key={template.id} className="group overflow-hidden hover:shadow-2xl transition-all duration-500 border-0 shadow-lg hover:scale-105">
                {/* Image Container with Overlay */}
                <div className="aspect-video bg-gradient-to-br from-blue-50 to-purple-50 relative overflow-hidden">
                  {template.preview_image ? (
                    <img
                      src={template.preview_image}
                      alt={template.title}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                  ) : (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center space-y-2">
                        <FileText className="h-12 w-12 text-blue-400 mx-auto" />
                        <p className="text-sm text-muted-foreground">Template Preview</p>
                      </div>
                    </div>
                  )}

                  {/* Overlay with Actions */}
                  <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center gap-3">
                    <Button size="sm" variant="secondary" className="bg-white/90 hover:bg-white">
                      <Eye className="h-4 w-4 mr-2" />
                      Preview
                    </Button>
                    <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                      <ShoppingCart className="h-4 w-4 mr-2" />
                      Buy Now
                    </Button>
                  </div>

                  {/* Price Badge */}
                  <div className="absolute top-4 right-4">
                    <Badge className="bg-white/90 text-gray-900 hover:bg-white">
                      ₹{template.price}
                    </Badge>
                  </div>

                  {/* Featured Badge */}
                  {template.is_featured && (
                    <div className="absolute top-4 left-4">
                      <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white border-0">
                        <Star className="h-3 w-3 mr-1" />
                        Featured
                      </Badge>
                    </div>
                  )}
                </div>

                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-xl font-semibold group-hover:text-blue-600 transition-colors">
                        {template.title}
                      </h3>
                      <p className="text-sm text-muted-foreground mt-2 line-clamp-2">
                        {template.description}
                      </p>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Eye className="h-4 w-4" />
                          <span>{template.view_count || 0}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Download className="h-4 w-4" />
                          <span>{template.download_count || 0}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                          <span>{template.rating_average || 0}</span>
                        </div>
                      </div>
                      <Button variant="ghost" size="sm" className="p-2">
                        <Bookmark className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        <div className="text-center">
          <Link href="/templates">
            <Button size="lg" variant="outline" className="px-8 py-3">
              View All Templates
              <ChevronRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </div>

      {/* Enhanced Features Grid */}
      <div className="space-y-8">
        <div className="text-center space-y-4">
          <h2 className="text-3xl font-bold">Why Choose KaleidoneX?</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Discover the powerful features that make KaleidoneX the best choice for your template needs
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center gap-2">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="h-6 w-6 text-blue-600" />
                </div>
                <CardTitle>50+ Premium Templates</CardTitle>
              </div>
              <CardDescription>
                Choose from a wide variety of professionally designed templates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Our template library includes designs for every industry and use case.
                From business websites to creative portfolios, find the perfect starting point.
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge variant="outline">Business</Badge>
                <Badge variant="outline">Portfolio</Badge>
                <Badge variant="outline">E-commerce</Badge>
                <Badge variant="outline">Blog</Badge>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center gap-2">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Palette className="h-6 w-6 text-purple-600" />
                </div>
                <CardTitle>Live Customization</CardTitle>
              </div>
              <CardDescription>
                Customize your templates in real-time with our visual editor
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                See your changes instantly as you customize colors, fonts, layouts, and content.
                No coding required - just point, click, and create.
              </p>
              <div className="flex items-center gap-2 text-sm text-green-600">
                <CheckCircle className="h-4 w-4" />
                <span>Real-time preview</span>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center gap-2">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Zap className="h-6 w-6 text-green-600" />
                </div>
                <CardTitle>Fast & Modern</CardTitle>
              </div>
              <CardDescription>
                Built with the latest technologies for optimal performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Powered by Next.js, Tailwind CSS, and modern web standards.
                Your templates will be fast, responsive, and SEO-friendly.
              </p>
              <div className="flex items-center gap-2 text-sm text-blue-600">
                <TrendingUp className="h-4 w-4" />
                <span>99.9% Uptime</span>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center gap-2">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Smartphone className="h-6 w-6 text-orange-600" />
                </div>
                <CardTitle>Mobile Responsive</CardTitle>
              </div>
              <CardDescription>
                All templates are fully responsive and mobile-optimized
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Your templates will look perfect on all devices - desktop, tablet, and mobile.
                Automatic responsive design ensures great user experience everywhere.
              </p>
              <div className="flex items-center gap-2 text-sm text-orange-600">
                <Globe className="h-4 w-4" />
                <span>Cross-platform</span>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center gap-2">
                <div className="p-2 bg-red-100 rounded-lg">
                  <Code className="h-6 w-6 text-red-600" />
                </div>
                <CardTitle>No Code Required</CardTitle>
              </div>
              <CardDescription>
                Create professional websites without writing a single line of code
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Our intuitive drag-and-drop interface makes it easy for anyone to create
                stunning websites, regardless of technical expertise.
              </p>
              <div className="flex items-center gap-2 text-sm text-red-600">
                <Heart className="h-4 w-4" />
                <span>User-friendly</span>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center gap-2">
                <div className="p-2 bg-indigo-100 rounded-lg">
                  <Layers className="h-6 w-6 text-indigo-600" />
                </div>
                <CardTitle>Advanced Features</CardTitle>
              </div>
              <CardDescription>
                Powerful features for professional websites and applications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Payment integration, user authentication, admin panels, analytics,
                and much more. Everything you need for a complete solution.
              </p>
              <div className="flex items-center gap-2 text-sm text-indigo-600">
                <Award className="h-4 w-4" />
                <span>Enterprise-ready</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Stats Section */}
      <div className="space-y-8">
        <div className="text-center space-y-4">
          <h2 className="text-3xl font-bold">Trusted by Thousands</h2>
          <p className="text-lg text-muted-foreground">
            Join the growing community of creators using KaleidoneX
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-4">
          <Card className="text-center hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="space-y-2">
                <div className="text-4xl font-bold text-blue-600">50+</div>
                <p className="text-sm font-medium">Premium Templates</p>
                <p className="text-xs text-muted-foreground">Professionally designed</p>
              </div>
            </CardContent>
          </Card>

          <Card className="text-center hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="space-y-2">
                <div className="text-4xl font-bold text-green-600">10K+</div>
                <p className="text-sm font-medium">Happy Users</p>
                <p className="text-xs text-muted-foreground">Worldwide community</p>
              </div>
            </CardContent>
          </Card>

          <Card className="text-center hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="space-y-2">
                <div className="text-4xl font-bold text-purple-600">24/7</div>
                <p className="text-sm font-medium">Support</p>
                <p className="text-xs text-muted-foreground">Always here to help</p>
              </div>
            </CardContent>
          </Card>

          <Card className="text-center hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="space-y-2">
                <div className="text-4xl font-bold text-orange-600">99.9%</div>
                <p className="text-sm font-medium">Uptime</p>
                <p className="text-xs text-muted-foreground">Reliable service</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Testimonials Section */}
      <div className="space-y-8">
        <div className="text-center space-y-4">
          <h2 className="text-3xl font-bold">What Our Users Say</h2>
          <p className="text-lg text-muted-foreground">
            Don't just take our word for it - hear from our satisfied customers
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          <Card className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="flex items-center gap-1">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 text-yellow-500 fill-current" />
                  ))}
                </div>
                <p className="text-sm text-muted-foreground">
                  "KaleidoneX made it incredibly easy to create a professional website for my business.
                  The templates are beautiful and the customization options are endless!"
                </p>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <Users className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Sarah Johnson</p>
                    <p className="text-xs text-muted-foreground">Small Business Owner</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="flex items-center gap-1">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 text-yellow-500 fill-current" />
                  ))}
                </div>
                <p className="text-sm text-muted-foreground">
                  "As a designer, I appreciate the attention to detail in every template.
                  The live preview feature saves me hours of work!"
                </p>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                    <Palette className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Mike Chen</p>
                    <p className="text-xs text-muted-foreground">Web Designer</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="flex items-center gap-1">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 text-yellow-500 fill-current" />
                  ))}
                </div>
                <p className="text-sm text-muted-foreground">
                  "The customer support is amazing! They helped me customize my template
                  exactly how I wanted it. Highly recommended!"
                </p>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <Heart className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Emily Davis</p>
                    <p className="text-xs text-muted-foreground">Freelancer</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* CTA Section */}
      <div className="text-center space-y-6 py-12 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl">
        <div className="space-y-4">
          <h2 className="text-3xl font-bold">Ready to Get Started?</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Join thousands of creators who are already building amazing websites with KaleidoneX.
            Start your journey today!
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Link href="/templates">
            <Button size="lg" className="text-lg px-8 py-6">
              <Rocket className="mr-2 h-5 w-5" />
              Get Started Now
            </Button>
          </Link>
          <Link href="/contact">
            <Button variant="outline" size="lg" className="text-lg px-8 py-6">
              <Clock className="mr-2 h-5 w-5" />
              Schedule Demo
            </Button>
          </Link>
        </div>

        <p className="text-sm text-muted-foreground">
          No credit card required • 14-day free trial • Cancel anytime
        </p>
      </div>

      {/* Footer */}
      <footer className="bg-gradient-to-r from-slate-900 to-slate-800 text-white rounded-2xl mt-16">
        <div className="px-8 py-12">
          {/* Main Footer Content */}
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            {/* Company Info */}
            <div className="space-y-4">
              <div className="space-y-2">
                <h3 className="text-2xl font-bold">KaleidoneX</h3>
                <p className="text-slate-300 text-sm leading-relaxed">
                  Creating stunning, customizable templates for modern web applications.
                  Build beautiful websites with our powerful design tools.
                </p>
              </div>

              {/* Social Links */}
              <div className="flex gap-3">
                <Button size="sm" variant="ghost" className="w-10 h-10 p-0 hover:bg-white/10">
                  <Facebook className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="ghost" className="w-10 h-10 p-0 hover:bg-white/10">
                  <Twitter className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="ghost" className="w-10 h-10 p-0 hover:bg-white/10">
                  <Instagram className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="ghost" className="w-10 h-10 p-0 hover:bg-white/10">
                  <Linkedin className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="ghost" className="w-10 h-10 p-0 hover:bg-white/10">
                  <Github className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Quick Links */}
            <div className="space-y-4">
              <h4 className="text-lg font-semibold">Quick Links</h4>
              <div className="space-y-2">
                <Link href="/templates" className="block text-slate-300 hover:text-white transition-colors text-sm">
                  Browse Templates
                </Link>
                <Link href="/customize" className="block text-slate-300 hover:text-white transition-colors text-sm">
                  Customize Templates
                </Link>
                <Link href="/contact" className="block text-slate-300 hover:text-white transition-colors text-sm">
                  Contact Us
                </Link>
                <Link href="/login" className="block text-slate-300 hover:text-white transition-colors text-sm">
                  Sign In
                </Link>
              </div>
            </div>

            {/* Categories */}
            <div className="space-y-4">
              <h4 className="text-lg font-semibold">Categories</h4>
              <div className="space-y-2">
                <Link href="/templates?category=Business" className="block text-slate-300 hover:text-white transition-colors text-sm">
                  Business Templates
                </Link>
                <Link href="/templates?category=Portfolio" className="block text-slate-300 hover:text-white transition-colors text-sm">
                  Portfolio Templates
                </Link>
                <Link href="/templates?category=E-commerce" className="block text-slate-300 hover:text-white transition-colors text-sm">
                  E-commerce Templates
                </Link>
                <Link href="/templates?category=Blog" className="block text-slate-300 hover:text-white transition-colors text-sm">
                  Blog Templates
                </Link>
              </div>
            </div>

            {/* Contact Info */}
            <div className="space-y-4">
              <h4 className="text-lg font-semibold">Get in Touch</h4>
              <div className="space-y-3">
                <div className="flex items-center gap-3 text-sm">
                  <Mail className="h-4 w-4 text-slate-400" />
                  <span className="text-slate-300"><EMAIL></span>
                </div>
                <div className="flex items-center gap-3 text-sm">
                  <Phone className="h-4 w-4 text-slate-400" />
                  <span className="text-slate-300">+****************</span>
                </div>
                <div className="flex items-center gap-3 text-sm">
                  <MapPin className="h-4 w-4 text-slate-400" />
                  <span className="text-slate-300">San Francisco, CA</span>
                </div>
              </div>

              {/* Newsletter */}
              <div className="space-y-2">
                <h5 className="text-sm font-medium">Stay Updated</h5>
                <div className="flex gap-2">
                  <input
                    type="email"
                    placeholder="Enter your email"
                    className="flex-1 px-3 py-2 text-sm bg-white/10 border border-white/20 rounded-md text-white placeholder:text-slate-400 focus:outline-none focus:ring-2 focus:ring-white/30"
                  />
                  <Button size="sm" variant="secondary">
                    Subscribe
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Footer Bottom */}
          <div className="border-t border-white/10 mt-8 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <div className="text-sm text-slate-400">
                © 2024 KaleidoneX. All rights reserved.
              </div>

              <div className="flex gap-6 text-sm">
                <Link href="/privacy" className="text-slate-400 hover:text-white transition-colors">
                  Privacy Policy
                </Link>
                <Link href="/terms" className="text-slate-400 hover:text-white transition-colors">
                  Terms of Service
                </Link>
                <Link href="/cookies" className="text-slate-400 hover:text-white transition-colors">
                  Cookie Policy
                </Link>
              </div>

              <div className="flex items-center gap-2 text-sm text-slate-400">
                <Heart className="h-4 w-4 text-red-400" />
                <span>Made with love</span>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
