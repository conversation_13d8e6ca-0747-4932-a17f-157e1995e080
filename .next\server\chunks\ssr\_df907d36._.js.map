{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { createClient } from \"@/lib/supabase/client\"\nimport { Database } from \"@/lib/database.types\"\nimport { toast } from \"sonner\"\nimport { useRouter } from \"next/navigation\"\nimport { FileText, Settings, ShoppingBag, User } from \"lucide-react\"\n\ntype Purchase = Database['public']['Tables']['purchases']['Row'] & {\n  templates: Database['public']['Tables']['templates']['Row']\n}\n\ntype Customization = Database['public']['Tables']['customizations']['Row']\n\nexport default function DashboardPage() {\n  const [user, setUser] = useState<any>(null)\n  const [purchases, setPurchases] = useState<Purchase[]>([])\n  const [customizations, setCustomizations] = useState<Customization[]>([])\n  const [loading, setLoading] = useState(true)\n\n  const supabase = createClient()\n  const router = useRouter()\n\n  useEffect(() => {\n    checkUser()\n  }, [])\n\n  const checkUser = async () => {\n    try {\n      const { data: { user }, error } = await supabase.auth.getUser()\n      \n      if (error || !user) {\n        router.push('/login')\n        return\n      }\n\n      setUser(user)\n      await Promise.all([\n        fetchPurchases(user.id),\n        fetchCustomizations(user.id)\n      ])\n    } catch (error) {\n      console.error('Error checking user:', error)\n      toast.error('Failed to load user data')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const fetchPurchases = async (userId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('purchases')\n        .select(`\n          *,\n          templates (*)\n        `)\n        .eq('user_id', userId)\n        .order('created_at', { ascending: false })\n\n      if (error) throw error\n      setPurchases(data || [])\n    } catch (error) {\n      console.error('Error fetching purchases:', error)\n      toast.error('Failed to load purchases')\n    }\n  }\n\n  const fetchCustomizations = async (userId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('customizations')\n        .select('*')\n        .eq('user_id', userId)\n        .order('created_at', { ascending: false })\n\n      if (error) throw error\n      setCustomizations(data || [])\n    } catch (error) {\n      console.error('Error fetching customizations:', error)\n      toast.error('Failed to load customizations')\n    }\n  }\n\n  const handleSignOut = async () => {\n    await supabase.auth.signOut()\n    router.push('/')\n  }\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div>\n          <h1 className=\"text-3xl font-bold tracking-tight\">Dashboard</h1>\n          <p className=\"text-muted-foreground\">Loading your dashboard...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-3xl font-bold tracking-tight\">Dashboard</h1>\n          <p className=\"text-muted-foreground\">\n            Welcome back, {user.user_metadata?.full_name || user.email}!\n          </p>\n        </div>\n        <Button variant=\"outline\" onClick={handleSignOut}>\n          Sign Out\n        </Button>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Templates Purchased</CardTitle>\n            <ShoppingBag className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{purchases.length}</div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Customizations</CardTitle>\n            <Settings className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{customizations.length}</div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Total Spent</CardTitle>\n            <FileText className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">\n              ₹{purchases.reduce((total, purchase) => total + purchase.amount, 0)}\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Account Status</CardTitle>\n            <User className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">Active</div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Purchased Templates */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Your Purchased Templates</CardTitle>\n          <CardDescription>\n            Templates you have purchased and can download\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {purchases.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <p className=\"text-muted-foreground\">No templates purchased yet.</p>\n              <Button className=\"mt-4\" onClick={() => router.push('/templates')}>\n                Browse Templates\n              </Button>\n            </div>\n          ) : (\n            <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n              {purchases.map((purchase) => (\n                <Card key={purchase.id}>\n                  <CardHeader>\n                    <CardTitle className=\"text-lg\">{purchase.templates.title}</CardTitle>\n                    <CardDescription>\n                      Purchased on {new Date(purchase.created_at).toLocaleDateString()}\n                    </CardDescription>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"flex justify-between items-center mb-4\">\n                      <Badge variant=\"secondary\">{purchase.templates.category}</Badge>\n                      <span className=\"font-bold\">₹{purchase.amount}</span>\n                    </div>\n                    <div className=\"flex gap-2\">\n                      <Button size=\"sm\" className=\"flex-1\">\n                        Download\n                      </Button>\n                      {purchase.templates.preview_url && (\n                        <Button \n                          variant=\"outline\" \n                          size=\"sm\"\n                          onClick={() => window.open(purchase.templates.preview_url!, '_blank')}\n                        >\n                          Preview\n                        </Button>\n                      )}\n                    </div>\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Saved Customizations */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Your Customizations</CardTitle>\n          <CardDescription>\n            Template customizations you have saved\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {customizations.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <p className=\"text-muted-foreground\">No customizations saved yet.</p>\n              <Button className=\"mt-4\" onClick={() => router.push('/customize')}>\n                Start Customizing\n              </Button>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {customizations.map((customization) => (\n                <Card key={customization.id}>\n                  <CardContent className=\"pt-6\">\n                    <div className=\"flex justify-between items-start\">\n                      <div>\n                        <h4 className=\"font-semibold mb-2\">\n                          Customization #{customization.id.slice(0, 8)}\n                        </h4>\n                        <div className=\"text-sm text-muted-foreground space-y-1\">\n                          <p>Navbar: {customization.navbar_style}</p>\n                          <p>Hero: {customization.hero_section}</p>\n                          <p>Footer: {customization.footer_style}</p>\n                          <p>Created: {new Date(customization.created_at).toLocaleDateString()}</p>\n                        </div>\n                      </div>\n                      <div className=\"flex gap-2\">\n                        <Button \n                          size=\"sm\" \n                          variant=\"outline\"\n                          onClick={() => router.push('/customize')}\n                        >\n                          Edit\n                        </Button>\n                        <Button size=\"sm\">\n                          Use Template\n                        </Button>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAVA;;;;;;;;;;AAkBe,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YAE7D,IAAI,SAAS,CAAC,MAAM;gBAClB,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,QAAQ;YACR,MAAM,QAAQ,GAAG,CAAC;gBAChB,eAAe,KAAK,EAAE;gBACtB,oBAAoB,KAAK,EAAE;aAC5B;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,aACL,MAAM,CAAC,CAAC;;;QAGT,CAAC,EACA,EAAE,CAAC,WAAW,QACd,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO,MAAM;YACjB,aAAa,QAAQ,EAAE;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO,MAAM;YACjB,kBAAkB,QAAQ,EAAE;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAE,WAAU;;oCAAwB;oCACpB,KAAK,aAAa,EAAE,aAAa,KAAK,KAAK;oCAAC;;;;;;;;;;;;;kCAG/D,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;kCAAe;;;;;;;;;;;;0BAMpD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;0CAEzB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CAAsB,UAAU,MAAM;;;;;;;;;;;;;;;;;kCAIzD,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CAAsB,eAAe,MAAM;;;;;;;;;;;;;;;;;kCAI9D,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;wCAAqB;wCAChC,UAAU,MAAM,CAAC,CAAC,OAAO,WAAa,QAAQ,SAAS,MAAM,EAAE;;;;;;;;;;;;;;;;;;kCAKvE,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;0CAElB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CAAqB;;;;;;;;;;;;;;;;;;;;;;;0BAM1C,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACT,UAAU,MAAM,KAAK,kBACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;8CACrC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;oCAAO,SAAS,IAAM,OAAO,IAAI,CAAC;8CAAe;;;;;;;;;;;iDAKrE,8OAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAW,SAAS,SAAS,CAAC,KAAK;;;;;;8DACxD,8OAAC,gIAAA,CAAA,kBAAe;;wDAAC;wDACD,IAAI,KAAK,SAAS,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;sDAGlE,8OAAC,gIAAA,CAAA,cAAW;;8DACV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAa,SAAS,SAAS,CAAC,QAAQ;;;;;;sEACvD,8OAAC;4DAAK,WAAU;;gEAAY;gEAAE,SAAS,MAAM;;;;;;;;;;;;;8DAE/C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DAAC,MAAK;4DAAK,WAAU;sEAAS;;;;;;wDAGpC,SAAS,SAAS,CAAC,WAAW,kBAC7B,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,OAAO,IAAI,CAAC,SAAS,SAAS,CAAC,WAAW,EAAG;sEAC7D;;;;;;;;;;;;;;;;;;;mCArBE,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;0BAmChC,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACT,eAAe,MAAM,KAAK,kBACzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;8CACrC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;oCAAO,SAAS,IAAM,OAAO,IAAI,CAAC;8CAAe;;;;;;;;;;;iDAKrE,8OAAC;4BAAI,WAAU;sCACZ,eAAe,GAAG,CAAC,CAAC,8BACnB,8OAAC,gIAAA,CAAA,OAAI;8CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;;gEAAqB;gEACjB,cAAc,EAAE,CAAC,KAAK,CAAC,GAAG;;;;;;;sEAE5C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;wEAAE;wEAAS,cAAc,YAAY;;;;;;;8EACtC,8OAAC;;wEAAE;wEAAO,cAAc,YAAY;;;;;;;8EACpC,8OAAC;;wEAAE;wEAAS,cAAc,YAAY;;;;;;;8EACtC,8OAAC;;wEAAE;wEAAU,IAAI,KAAK,cAAc,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;8DAGtE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,OAAO,IAAI,CAAC;sEAC5B;;;;;;sEAGD,8OAAC,kIAAA,CAAA,SAAM;4DAAC,MAAK;sEAAK;;;;;;;;;;;;;;;;;;;;;;;mCAtBf,cAAc,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoC3C", "debugId": null}}, {"offset": {"line": 834, "column": 0}, "map": {"version": 3, "file": "shopping-bag.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/node_modules/lucide-react/src/icons/shopping-bag.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z', key: 'hou9p0' }],\n  ['path', { d: 'M3 6h18', key: 'd0wm0j' }],\n  ['path', { d: 'M16 10a4 4 0 0 1-8 0', key: '1ltviw' }],\n];\n\n/**\n * @component @name ShoppingBag\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNiAyIDMgNnYxNGEyIDIgMCAwIDAgMiAyaDE0YTIgMiAwIDAgMCAyLTJWNmwtMy00WiIgLz4KICA8cGF0aCBkPSJNMyA2aDE4IiAvPgogIDxwYXRoIGQ9Ik0xNiAxMGE0IDQgMCAwIDEtOCAwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/shopping-bag\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ShoppingBag = createLucideIcon('shopping-bag', __iconNode);\n\nexport default ShoppingBag;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAsD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACnF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAwB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACvD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}