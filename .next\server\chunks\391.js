exports.id=391,exports.ids=[391],exports.modules={4780:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var r=s(49384),a=s(82348);function n(...e){return(0,a.QP)((0,r.$)(e))}},25305:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},29523:(e,t,s)=>{"use strict";s.d(t,{$:()=>d});var r=s(60687);s(43210);var a=s(8730),n=s(24224),i=s(4780);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:s,asChild:n=!1,...d}){let l=n?a.DX:"button";return(0,r.jsx)(l,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:s,className:e})),...d})}},38457:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},53455:(e,t,s)=>{"use strict";s.d(t,{MainLayout:()=>O});var r=s(60687),a=s(43210),n=s(85814),i=s.n(n),o=s(16189),d=s(4780),l=s(29523),c=s(96834),u=s(32192),m=s(10022),h=s(98971),x=s(41550),v=s(49625),f=s(99891),p=s(58869),g=s(84027),b=s(40083),j=s(98712),y=s(79481),w=s(52581);let N=[{name:"Home",href:"/",icon:u.A},{name:"Templates",href:"/templates",icon:m.A},{name:"Customize",href:"/customize",icon:h.A},{name:"Contact",href:"/contact",icon:x.A}];function k({className:e}){let t,s=(0,o.usePathname)(),n=(0,o.useRouter)(),u=(0,y.U)(),[m,h]=(0,a.useState)(null),[x,k]=(0,a.useState)(null),[P,z]=(0,a.useState)(!0),A=async()=>{try{let{error:e}=await u.auth.signOut();if(e)throw e;w.oR.success("Signed out successfully"),n.push("/")}catch(e){console.error("Sign out error:",e),w.oR.error("Failed to sign out")}};return(0,r.jsxs)("div",{className:(0,d.cn)("flex flex-col h-full",e),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between px-6 py-4 border-b",children:[(0,r.jsx)("h1",{className:"text-xl font-bold",children:"KaleidoneX"}),x?.role==="admin"&&(0,r.jsxs)(c.E,{variant:"secondary",className:"text-xs",children:[(0,r.jsx)(f.A,{className:"h-3 w-3 mr-1"}),"Admin"]})]}),(0,r.jsx)("nav",{className:"flex-1 px-4 py-6 space-y-2",children:(t=[...N],m&&(t.push({name:"Dashboard",href:"/dashboard",icon:v.A}),x?.role==="admin"&&t.push({name:"Admin Panel",href:"/admin",icon:f.A})),t).map(e=>{let t=s===e.href;return(0,r.jsx)(i(),{href:e.href,children:(0,r.jsxs)(l.$,{variant:t?"secondary":"ghost",className:(0,d.cn)("w-full justify-start gap-3",t&&"bg-secondary"),children:[(0,r.jsx)(e.icon,{className:"h-4 w-4"}),e.name,"Admin Panel"===e.name&&(0,r.jsx)(c.E,{variant:"outline",className:"ml-auto text-xs",children:"Admin"})]})},e.name)})}),(0,r.jsx)("div",{className:"px-4 py-4 border-t",children:P?(0,r.jsxs)("div",{className:"flex items-center gap-3 px-3 py-2",children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-muted border-t-primary rounded-full animate-spin"}),(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:"Loading..."})]}):m?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 px-3 py-2 rounded-md bg-muted/50",children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center",children:(0,r.jsx)(p.A,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-medium truncate",children:x?.full_name||m.email?.split("@")[0]||"User"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground truncate",children:m.email})]})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)(i(),{href:"/dashboard",children:(0,r.jsxs)(l.$,{variant:"ghost",size:"sm",className:"w-full justify-start gap-3",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),"Settings"]})}),(0,r.jsxs)(l.$,{variant:"ghost",size:"sm",className:"w-full justify-start gap-3 text-red-600 hover:text-red-700 hover:bg-red-50",onClick:A,children:[(0,r.jsx)(b.A,{className:"h-4 w-4"}),"Sign Out"]})]})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(i(),{href:"/login",children:(0,r.jsxs)(l.$,{className:"w-full justify-start gap-3",children:[(0,r.jsx)(j.A,{className:"h-4 w-4"}),"Sign In"]})}),(0,r.jsx)(i(),{href:"/login",children:(0,r.jsxs)(l.$,{variant:"outline",className:"w-full justify-start gap-3",children:[(0,r.jsx)(p.A,{className:"h-4 w-4"}),"Sign Up"]})})]})})]})}var P=s(26134),z=s(11860);function A({...e}){return(0,r.jsx)(P.bL,{"data-slot":"sheet",...e})}function C({...e}){return(0,r.jsx)(P.l9,{"data-slot":"sheet-trigger",...e})}function I({...e}){return(0,r.jsx)(P.ZL,{"data-slot":"sheet-portal",...e})}function S({className:e,...t}){return(0,r.jsx)(P.hJ,{"data-slot":"sheet-overlay",className:(0,d.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function L({className:e,children:t,side:s="right",...a}){return(0,r.jsxs)(I,{children:[(0,r.jsx)(S,{}),(0,r.jsxs)(P.UC,{"data-slot":"sheet-content",className:(0,d.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===s&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===s&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===s&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===s&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...a,children:[t,(0,r.jsxs)(P.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,r.jsx)(z.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}var M=s(12941),D=s(10218);let $=({...e})=>{let{theme:t="system"}=(0,D.D)();return(0,r.jsx)(w.l$,{theme:t,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})};function O({children:e}){let[t,s]=(0,a.useState)(!1),n=function(){let[e,t]=a.useState(void 0);return a.useEffect(()=>{let e=window.matchMedia("(max-width: 767px)"),s=()=>{t(window.innerWidth<768)};return e.addEventListener("change",s),t(window.innerWidth<768),()=>e.removeEventListener("change",s)},[]),!!e}();(0,o.usePathname)();let[i,d]=(0,a.useState)(null);return(0,r.jsxs)("div",{className:"flex h-screen bg-background",children:[!n&&(0,r.jsx)("div",{className:"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0",children:(0,r.jsx)("div",{className:"flex flex-col flex-grow border-r bg-card",children:(0,r.jsx)(k,{})})}),n&&(0,r.jsx)(A,{open:t,onOpenChange:s,children:(0,r.jsx)(L,{side:"left",className:"p-0 w-64",children:(0,r.jsx)(k,{})})}),(0,r.jsxs)("div",{className:`flex flex-col flex-1 ${!n?"md:pl-64":""}`,children:[(0,r.jsxs)("header",{className:"flex items-center justify-between px-6 py-4 bg-card border-b",children:[n&&(0,r.jsx)(A,{open:t,onOpenChange:s,children:(0,r.jsx)(C,{asChild:!0,children:(0,r.jsx)(l.$,{variant:"ghost",size:"icon",children:(0,r.jsx)(M.A,{className:"h-5 w-5"})})})}),(0,r.jsx)("div",{className:"flex items-center space-x-4 ml-auto",children:(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:"Welcome back!"})})]}),(0,r.jsx)("main",{className:"flex-1 overflow-auto p-6",children:e})]}),(0,r.jsx)($,{})]})}},54293:(e,t,s)=>{Promise.resolve().then(s.bind(s,54959))},54959:(e,t,s)=>{"use strict";s.d(t,{MainLayout:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call MainLayout() from the server but MainLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\components\\layout\\main-layout.tsx","MainLayout")},61135:()=>{},79481:(e,t,s)=>{"use strict";s.d(t,{U:()=>a});var r=s(72032);function a(){let e="https://aovrwjzhqrgbdhszdowg.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFvdnJ3anpocXJnYmRoc3pkb3dnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4NTI5MjEsImV4cCI6MjA2NDQyODkyMX0.9TWCiuDturnqdOSlDeOWVroegkTM7Nra-W2LUoyGDSs";if(!e||!t)throw Error("Missing Supabase environment variables. Please check your .env.local file.");return(0,r.createBrowserClient)(e,t)}},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c,metadata:()=>l});var r=s(37413),a=s(22376),n=s.n(a),i=s(68726),o=s.n(i),d=s(54959);s(61135);let l={title:"KaleidoneX - Template Builder",description:"Create stunning, customizable templates with our powerful design tools"};function c({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${n().variable} ${o().variable} antialiased`,children:(0,r.jsx)(d.MainLayout,{children:e})})})}},96149:(e,t,s)=>{Promise.resolve().then(s.bind(s,53455))},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>d});var r=s(60687);s(43210);var a=s(8730),n=s(24224),i=s(4780);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:s=!1,...n}){let d=s?a.DX:"span";return(0,r.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(o({variant:t}),e),...n})}}};