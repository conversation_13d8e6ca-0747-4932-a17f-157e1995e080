{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_061ca3cf._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_452e0654.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "903ou+si8jPmiJ9R1U8PjxHzUt1mwAm+hUYuJyHsngs=", "__NEXT_PREVIEW_MODE_ID": "f310b702f0461d8adab09797b83fcb96", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fa0f66cbf5d36e99886a2ddc0a8da3360a287f7e3de2cef5c324c941455674b7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "386b89e901357ac6d0fa7e56559eca989880c66d472cb52e56b08144d91eaec5"}}}, "sortedMiddleware": ["/"], "functions": {}}