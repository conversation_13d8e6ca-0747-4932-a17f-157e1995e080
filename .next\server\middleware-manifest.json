{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_061ca3cf._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_452e0654.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "903ou+si8jPmiJ9R1U8PjxHzUt1mwAm+hUYuJyHsngs=", "__NEXT_PREVIEW_MODE_ID": "ab180838f9e2b6cbf47e6365689c2603", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "25f95bed1f1be9e02446e96e215064a0b9a0dcc561b66bc65c308c30b20cd663", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a2e5fa075c21c24567cc36be3c0bf04769e843643f1077f482befda016ac5a96"}}}, "sortedMiddleware": ["/"], "functions": {}}