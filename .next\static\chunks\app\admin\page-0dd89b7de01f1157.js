(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[698],{283:(e,t,s)=>{Promise.resolve().then(s.bind(s,9596))},285:(e,t,s)=>{"use strict";s.d(t,{$:()=>c});var a=s(5155);s(2115);var r=s(9708),i=s(2085),n=s(9434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:s,size:i,asChild:c=!1,...d}=e,o=c?r.DX:"button";return(0,a.jsx)(o,{"data-slot":"button",className:(0,n.cn)(l({variant:s,size:i,className:t})),...d})}},2523:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var a=s(5155);s(2115);var r=s(9434);function i(e){let{className:t,type:s,...i}=e;return(0,a.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},2643:(e,t,s)=>{"use strict";s.d(t,{U:()=>r});var a=s(1935);function r(){let e="https://aovrwjzhqrgbdhszdowg.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFvdnJ3anpocXJnYmRoc3pkb3dnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4NTI5MjEsImV4cCI6MjA2NDQyODkyMX0.9TWCiuDturnqdOSlDeOWVroegkTM7Nra-W2LUoyGDSs";if(!e||!t)throw Error("Missing Supabase environment variables. Please check your .env.local file.");return(0,a.createBrowserClient)(e,t)}},5057:(e,t,s)=>{"use strict";s.d(t,{J:()=>n});var a=s(5155);s(2115);var r=s(968),i=s(9434);function n(e){let{className:t,...s}=e;return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...s})}},6126:(e,t,s)=>{"use strict";s.d(t,{E:()=>c});var a=s(5155);s(2115);var r=s(9708),i=s(2085),n=s(9434);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:s,asChild:i=!1,...c}=e,d=i?r.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(l({variant:s}),t),...c})}},6695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>n});var a=s(5155);s(2115);var r=s(9434);function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...s})}},7313:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>c,av:()=>d,j7:()=>l,tU:()=>n});var a=s(5155);s(2115);var r=s(64),i=s(9434);function n(e){let{className:t,...s}=e;return(0,a.jsx)(r.bL,{"data-slot":"tabs",className:(0,i.cn)("flex flex-col gap-2",t),...s})}function l(e){let{className:t,...s}=e;return(0,a.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,i.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,i.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,i.cn)("flex-1 outline-none",t),...s})}},8539:(e,t,s)=>{"use strict";s.d(t,{T:()=>i});var a=s(5155);s(2115);var r=s(9434);function i(e){let{className:t,...s}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...s})}},9409:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>x,gC:()=>m,l6:()=>d,yv:()=>o});var a=s(5155);s(2115);var r=s(1146),i=s(6474),n=s(5196),l=s(7863),c=s(9434);function d(e){let{...t}=e;return(0,a.jsx)(r.bL,{"data-slot":"select",...t})}function o(e){let{...t}=e;return(0,a.jsx)(r.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:s="default",children:n,...l}=e;return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":s,className:(0,c.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l,children:[n,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:t,children:s,position:i="popper",...n}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,c.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:i,...n,children:[(0,a.jsx)(p,{}),(0,a.jsx)(r.LM,{className:(0,c.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:s}),(0,a.jsx)(h,{})]})})}function x(e){let{className:t,children:s,...i}=e;return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,c.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...i,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(n.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:s})]})}function p(e){let{className:t,...s}=e;return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(l.A,{className:"size-4"})})}function h(e){let{className:t,...s}=e;return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(i.A,{className:"size-4"})})}},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i});var a=s(2596),r=s(9688);function i(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}},9596:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>I});var a=s(5155),r=s(2115),i=s(6695),n=s(285),l=s(6126),c=s(7313),d=s(5452),o=s(4416),u=s(9434);let m=d.bL;d.l9;let x=d.ZL;d.bm;let p=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(d.hJ,{ref:t,className:(0,u.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...r})});p.displayName=d.hJ.displayName;let h=r.forwardRef((e,t)=>{let{className:s,children:r,...i}=e;return(0,a.jsxs)(x,{children:[(0,a.jsx)(p,{}),(0,a.jsxs)(d.UC,{ref:t,className:(0,u.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...i,children:[r,(0,a.jsxs)(d.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(o.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});h.displayName=d.UC.displayName;let f=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,u.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...s})};f.displayName="DialogHeader";let g=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(d.hE,{ref:t,className:(0,u.cn)("text-lg font-semibold leading-none tracking-tight",s),...r})});g.displayName=d.hE.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(d.VY,{ref:t,className:(0,u.cn)("text-sm text-muted-foreground",s),...r})}).displayName=d.VY.displayName;var v=s(5057),j=s(2523),b=s(8539),w=s(9409),y=s(2643),N=s(6671),_=s(5695),k=s(7434),S=s(8883),C=s(6151),R=s(381),z=s(7580),U=s(1788),A=s(1492),L=s(2525),D=s(4616),E=s(3786),T=s(3717);function I(){let[e,t]=(0,r.useState)(null),[s,n]=(0,r.useState)(!1),[l,d]=(0,r.useState)(!0),[o,u]=(0,r.useState)([]),[m,x]=(0,r.useState)([]),[p,h]=(0,r.useState)([]),[f,g]=(0,r.useState)([]),[v,j]=(0,r.useState)([]),[b,w]=(0,r.useState)("contacts"),U=(0,y.U)(),A=(0,_.useRouter)();(0,r.useEffect)(()=>{L()},[]);let L=async()=>{try{let{data:{user:e},error:s}=await U.auth.getUser();if(s||!e)return void A.push("/");t(e);let{data:a,error:r}=await U.from("profiles").select("role").eq("id",e.id).single();if(r||!a||"admin"!==a.role)return void A.push("/");n(!0),await D()}catch(e){console.error("Error checking admin access:",e),A.push("/")}finally{d(!1)}},D=async()=>{try{await Promise.all([E(),T(),I(),F(),V()])}catch(e){console.error("Error loading admin data:",e),N.oR.error("Failed to load admin data")}},E=async()=>{let{data:e,error:t}=await U.from("contact_requests").select("*").order("created_at",{ascending:!1});if(t)throw t;u(e||[])},T=async()=>{let{data:e,error:t}=await U.from("purchases").select("\n        *,\n        templates (*),\n        profiles (*)\n      ").order("created_at",{ascending:!1});if(t)throw t;x(e||[])},I=async()=>{let{data:e,error:t}=await U.from("customizations").select("\n        *,\n        profiles (*)\n      ").order("created_at",{ascending:!1});if(t)throw t;h(e||[])},F=async()=>{let{data:e,error:t}=await U.from("visitor_logs").select("*").order("created_at",{ascending:!1}).limit(1e3);if(t)throw t;g(e||[])},V=async()=>{let{data:e,error:t}=await U.from("templates").select("*").order("created_at",{ascending:!1});if(t)throw t;j(e||[])};return l?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-2",children:"Loading Admin Panel..."}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Verifying admin access"})]})}):s?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Admin Panel"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage your application data and analytics"})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-5",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Templates"}),(0,a.jsx)(k.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:v.length})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Contact Requests"}),(0,a.jsx)(S.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:o.length})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Total Purchases"}),(0,a.jsx)(C.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:m.length})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Customizations"}),(0,a.jsx)(R.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:p.length})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Visitor Logs"}),(0,a.jsx)(z.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:f.length})})]})]}),(0,a.jsxs)(c.tU,{value:b,onValueChange:w,children:[(0,a.jsxs)(c.j7,{className:"grid w-full grid-cols-5",children:[(0,a.jsx)(c.Xi,{value:"templates",children:"Templates"}),(0,a.jsx)(c.Xi,{value:"contacts",children:"Contact Requests"}),(0,a.jsx)(c.Xi,{value:"purchases",children:"Purchases"}),(0,a.jsx)(c.Xi,{value:"customizations",children:"Customizations"}),(0,a.jsx)(c.Xi,{value:"visitors",children:"Visitor Logs"})]}),(0,a.jsx)(c.av,{value:"templates",className:"space-y-4",children:(0,a.jsx)(P,{data:v,onRefresh:V})}),(0,a.jsx)(c.av,{value:"contacts",className:"space-y-4",children:(0,a.jsx)(q,{data:o,onRefresh:E})}),(0,a.jsx)(c.av,{value:"purchases",className:"space-y-4",children:(0,a.jsx)(O,{data:m,onRefresh:T})}),(0,a.jsx)(c.av,{value:"customizations",className:"space-y-4",children:(0,a.jsx)(B,{data:p,onRefresh:I})}),(0,a.jsx)(c.av,{value:"visitors",className:"space-y-4",children:(0,a.jsx)(Z,{data:f,onRefresh:F})})]})]}):null}function q(e){let{data:t,onRefresh:s}=e,l=(0,y.U)(),[c,d]=(0,r.useState)("created_at"),[o,u]=(0,r.useState)("desc"),m=[...t].sort((e,t)=>{let s=e[c],a=t[c];return s<a?"asc"===o?-1:1:s>a?"asc"===o?1:-1:0}),x=e=>{c===e?u("asc"===o?"desc":"asc"):(d(e),u("asc"))},p=async e=>{if(confirm("Are you sure you want to delete this contact request?"))try{let{error:t}=await l.from("contact_requests").delete().eq("id",e);if(t)throw t;N.oR.success("Contact request deleted successfully"),s()}catch(e){console.error("Error deleting contact request:",e),N.oR.error("Failed to delete contact request")}};return(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(i.ZB,{children:"Contact Requests"}),(0,a.jsx)(i.BT,{children:"Manage customer inquiries and messages"})]}),(0,a.jsx)("div",{className:"flex gap-2",children:(0,a.jsxs)(n.$,{variant:"outline",onClick:()=>{let e=new Blob([[["Name","Email","Message","Created At"],...m.map(e=>[e.name,e.email,e.message,new Date(e.created_at).toLocaleString()])].map(e=>e.map(e=>'"'.concat(e,'"')).join(",")).join("\n")],{type:"text/csv"}),t=window.URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download="contact-requests-".concat(new Date().toISOString().split("T")[0],".csv"),s.click(),window.URL.revokeObjectURL(t)},children:[(0,a.jsx)(U.A,{className:"h-4 w-4 mr-2"}),"Export CSV"]})})]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[m.map(e=>(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.email})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>x("created_at"),children:(0,a.jsx)(A.A,{className:"h-4 w-4"})}),(0,a.jsx)(n.$,{variant:"destructive",size:"sm",onClick:()=>p(e.id),children:(0,a.jsx)(L.A,{className:"h-4 w-4"})})]})]}),(0,a.jsx)("p",{className:"text-sm mb-2",children:e.message}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:new Date(e.created_at).toLocaleString()})]},e.id)),0===m.length&&(0,a.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:"No contact requests found"})]})})]})}function O(e){let{data:t,onRefresh:s}=e,c=(0,y.U)(),[d,o]=(0,r.useState)("created_at"),[u,m]=(0,r.useState)("desc"),x=[...t].sort((e,t)=>{let s=e[d],a=t[d];return s<a?"asc"===u?-1:1:s>a?"asc"===u?1:-1:0}),p=e=>{d===e?m("asc"===u?"desc":"asc"):(o(e),m("asc"))},h=async e=>{if(confirm("Are you sure you want to delete this purchase record?"))try{let{error:t}=await c.from("purchases").delete().eq("id",e);if(t)throw t;N.oR.success("Purchase record deleted successfully"),s()}catch(e){console.error("Error deleting purchase:",e),N.oR.error("Failed to delete purchase record")}},f=x.reduce((e,t)=>e+t.amount,0);return(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(i.ZB,{children:"Purchases"}),(0,a.jsxs)(i.BT,{children:["Total Revenue: ₹",f," • ",x.length," purchases"]})]}),(0,a.jsx)("div",{className:"flex gap-2",children:(0,a.jsxs)(n.$,{variant:"outline",onClick:()=>{let e=new Blob([[["User Email","Template","Amount","Currency","Status","Payment ID","Created At"],...x.map(e=>{var t,s;return[(null==(t=e.profiles)?void 0:t.full_name)||"Unknown",(null==(s=e.templates)?void 0:s.title)||"Unknown Template",e.amount.toString(),e.currency,e.status,e.razorpay_payment_id,new Date(e.created_at).toLocaleString()]})].map(e=>e.map(e=>'"'.concat(e,'"')).join(",")).join("\n")],{type:"text/csv"}),t=window.URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download="purchases-".concat(new Date().toISOString().split("T")[0],".csv"),s.click(),window.URL.revokeObjectURL(t)},children:[(0,a.jsx)(U.A,{className:"h-4 w-4 mr-2"}),"Export CSV"]})})]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[x.map(e=>{var t,s;return(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold",children:null==(t=e.templates)?void 0:t.title}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:(null==(s=e.profiles)?void 0:s.full_name)||"Unknown User"})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>p("amount"),children:(0,a.jsx)(A.A,{className:"h-4 w-4"})}),(0,a.jsx)(n.$,{variant:"destructive",size:"sm",onClick:()=>h(e.id),children:(0,a.jsx)(L.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Amount:"}),(0,a.jsxs)("p",{className:"font-medium",children:["₹",e.amount]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Status:"}),(0,a.jsx)(l.E,{variant:"completed"===e.status?"default":"secondary",children:e.status})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Payment ID:"}),(0,a.jsx)("p",{className:"font-mono text-xs",children:e.razorpay_payment_id})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Date:"}),(0,a.jsx)("p",{children:new Date(e.created_at).toLocaleDateString()})]})]})]},e.id)}),0===x.length&&(0,a.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:"No purchases found"})]})})]})}function B(e){let{data:t,onRefresh:s}=e,l=(0,y.U)(),[c,d]=(0,r.useState)("created_at"),[o,u]=(0,r.useState)("desc"),m=[...t].sort((e,t)=>{let s=e[c],a=t[c];return s<a?"asc"===o?-1:1:s>a?"asc"===o?1:-1:0}),x=e=>{c===e?u("asc"===o?"desc":"asc"):(d(e),u("asc"))},p=async e=>{if(confirm("Are you sure you want to delete this customization?"))try{let{error:t}=await l.from("customizations").delete().eq("id",e);if(t)throw t;N.oR.success("Customization deleted successfully"),s()}catch(e){console.error("Error deleting customization:",e),N.oR.error("Failed to delete customization")}};return(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(i.ZB,{children:"Customizations"}),(0,a.jsx)(i.BT,{children:"User template customization sessions"})]}),(0,a.jsx)("div",{className:"flex gap-2",children:(0,a.jsxs)(n.$,{variant:"outline",onClick:()=>{let e=new Blob([[["User","Navbar Style","Hero Section","Footer Style","Created At","Updated At"],...m.map(e=>{var t;return[(null==(t=e.profiles)?void 0:t.full_name)||"Unknown",e.navbar_style,e.hero_section,e.footer_style,new Date(e.created_at).toLocaleString(),new Date(e.updated_at).toLocaleString()]})].map(e=>e.map(e=>'"'.concat(e,'"')).join(",")).join("\n")],{type:"text/csv"}),t=window.URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download="customizations-".concat(new Date().toISOString().split("T")[0],".csv"),s.click(),window.URL.revokeObjectURL(t)},children:[(0,a.jsx)(U.A,{className:"h-4 w-4 mr-2"}),"Export CSV"]})})]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[m.map(e=>{var t;return(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold",children:(null==(t=e.profiles)?void 0:t.full_name)||"Unknown User"}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["ID: ",e.id.slice(0,8),"..."]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>x("created_at"),children:(0,a.jsx)(A.A,{className:"h-4 w-4"})}),(0,a.jsx)(n.$,{variant:"destructive",size:"sm",onClick:()=>p(e.id),children:(0,a.jsx)(L.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Navbar:"}),(0,a.jsx)("p",{className:"font-medium",children:e.navbar_style})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Hero:"}),(0,a.jsx)("p",{className:"font-medium",children:e.hero_section})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Footer:"}),(0,a.jsx)("p",{className:"font-medium",children:e.footer_style})]})]}),(0,a.jsxs)("div",{className:"mt-2 text-xs text-muted-foreground",children:["Created: ",new Date(e.created_at).toLocaleString()," • Updated: ",new Date(e.updated_at).toLocaleString()]})]},e.id)}),0===m.length&&(0,a.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:"No customizations found"})]})})]})}function Z(e){let{data:t,onRefresh:s}=e,l=(0,y.U)(),[c,d]=(0,r.useState)("created_at"),[o,u]=(0,r.useState)("desc"),m=[...t].sort((e,t)=>{let s=e[c],a=t[c];return s<a?"asc"===o?-1:1:s>a?"asc"===o?1:-1:0}),x=e=>{c===e?u("asc"===o?"desc":"asc"):(d(e),u("asc"))},p=async e=>{if(confirm("Are you sure you want to delete this visitor log?"))try{let{error:t}=await l.from("visitor_logs").delete().eq("id",e);if(t)throw t;N.oR.success("Visitor log deleted successfully"),s()}catch(e){console.error("Error deleting visitor log:",e),N.oR.error("Failed to delete visitor log")}},h=async()=>{if(confirm("Are you sure you want to delete logs older than 30 days?"))try{let e=new Date;e.setDate(e.getDate()-30);let{error:t}=await l.from("visitor_logs").delete().lt("created_at",e.toISOString());if(t)throw t;N.oR.success("Old visitor logs cleared successfully"),s()}catch(e){console.error("Error clearing old logs:",e),N.oR.error("Failed to clear old logs")}},f=new Set(m.map(e=>e.ip_address)).size,g=Object.entries(m.reduce((e,t)=>(e[t.path]=(e[t.path]||0)+1,e),{})).sort((e,t)=>{let[,s]=e,[,a]=t;return a-s}).slice(0,5);return(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(i.ZB,{children:"Visitor Logs"}),(0,a.jsxs)(i.BT,{children:[m.length," visits • ",f," unique IPs"]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(n.$,{variant:"outline",onClick:h,children:[(0,a.jsx)(L.A,{className:"h-4 w-4 mr-2"}),"Clear Old"]}),(0,a.jsxs)(n.$,{variant:"outline",onClick:()=>{let e=new Blob([[["IP Address","Path","User Agent","Created At"],...m.map(e=>[e.ip_address||"Unknown",e.path,e.user_agent||"Unknown",new Date(e.created_at).toLocaleString()])].map(e=>e.map(e=>'"'.concat(e,'"')).join(",")).join("\n")],{type:"text/csv"}),t=window.URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download="visitor-logs-".concat(new Date().toISOString().split("T")[0],".csv"),s.click(),window.URL.revokeObjectURL(t)},children:[(0,a.jsx)(U.A,{className:"h-4 w-4 mr-2"}),"Export CSV"]})]})]})}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsxs)("div",{className:"mb-6 p-4 bg-muted rounded-lg",children:[(0,a.jsx)("h4",{className:"font-semibold mb-2",children:"Top Visited Pages"}),(0,a.jsx)("div",{className:"space-y-1",children:g.map(e=>{let[t,s]=e;return(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:t}),(0,a.jsxs)("span",{className:"font-medium",children:[s," visits"]})]},t)})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[m.map(e=>(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold",children:e.path}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["IP: ",e.ip_address||"Unknown"]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>x("created_at"),children:(0,a.jsx)(A.A,{className:"h-4 w-4"})}),(0,a.jsx)(n.$,{variant:"destructive",size:"sm",onClick:()=>p(e.id),children:(0,a.jsx)(L.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("p",{className:"text-muted-foreground mb-1",children:"User Agent:"}),(0,a.jsx)("p",{className:"font-mono text-xs break-all",children:e.user_agent||"Unknown"})]}),(0,a.jsx)("div",{className:"mt-2 text-xs text-muted-foreground",children:new Date(e.created_at).toLocaleString()})]},e.id)),0===m.length&&(0,a.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:"No visitor logs found"})]})]})]})}function P(e){let{data:t,onRefresh:s}=e,[c,d]=(0,r.useState)("created_at"),[o,u]=(0,r.useState)("desc"),[m,x]=(0,r.useState)(!1),[p,h]=(0,r.useState)(null),f=(0,y.U)(),g=[...t].sort((e,t)=>{let s=e[c],a=t[c];if("price"===c)return"asc"===o?s-a:a-s;let r=String(s).localeCompare(String(a));return"asc"===o?r:-r}),v=async e=>{if(confirm("Are you sure you want to delete this template?"))try{let{error:t}=await f.from("templates").delete().eq("id",e);if(t)throw t;N.oR.success("Template deleted successfully"),s()}catch(e){console.error("Error deleting template:",e),N.oR.error("Failed to delete template")}};return(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(i.ZB,{children:"Templates Management"}),(0,a.jsx)(i.BT,{children:"Manage your template collection"})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(n.$,{onClick:()=>x(!0),children:[(0,a.jsx)(D.A,{className:"h-4 w-4 mr-2"}),"Add Template"]}),(0,a.jsxs)(n.$,{variant:"outline",onClick:()=>{let e=new Blob([[["Title","Description","Price","Category","Preview URL","Created At"],...g.map(e=>[e.title,e.description,e.price.toString(),e.category,e.preview_url||"",new Date(e.created_at).toLocaleDateString()])].map(e=>e.map(e=>'"'.concat(e,'"')).join(",")).join("\n")],{type:"text/csv"}),t=window.URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download="templates-".concat(new Date().toISOString().split("T")[0],".csv"),s.click(),window.URL.revokeObjectURL(t)},children:[(0,a.jsx)(U.A,{className:"h-4 w-4 mr-2"}),"Export CSV"]})]})]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[g.map(e=>(0,a.jsx)("div",{className:"border rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("h4",{className:"font-semibold text-lg",children:e.title}),(0,a.jsx)(l.E,{variant:"secondary",children:e.category})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm",children:[(0,a.jsxs)("span",{className:"font-medium text-green-600",children:["₹",e.price]}),e.preview_url&&(0,a.jsxs)("a",{href:e.preview_url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 flex items-center gap-1",children:[(0,a.jsx)(E.A,{className:"h-3 w-3"}),"Preview Link"]}),(0,a.jsxs)("span",{className:"text-muted-foreground",children:["Created: ",new Date(e.created_at).toLocaleDateString()]})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>h(e),children:(0,a.jsx)(T.A,{className:"h-4 w-4"})}),(0,a.jsx)(n.$,{variant:"destructive",size:"sm",onClick:()=>v(e.id),children:(0,a.jsx)(L.A,{className:"h-4 w-4"})})]})]})},e.id)),0===g.length&&(0,a.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:"No templates found"})]})}),(0,a.jsx)(F,{open:m||!!p,onClose:()=>{x(!1),h(null)},template:p,onSuccess:()=>{x(!1),h(null),s()}})]})}function F(e){let{open:t,onClose:s,template:i,onSuccess:l}=e,[c,d]=(0,r.useState)({title:"",description:"",price:"",category:"",preview_url:"",preview_image:""}),[o,u]=(0,r.useState)(!1),x=(0,y.U)();(0,r.useEffect)(()=>{i?d({title:i.title,description:i.description,price:i.price.toString(),category:i.category,preview_url:i.preview_url||"",preview_image:i.preview_image||""}):d({title:"",description:"",price:"",category:"",preview_url:"",preview_image:""})},[i]);let p=async e=>{e.preventDefault(),u(!0);try{let e={title:c.title,description:c.description,price:parseInt(c.price),category:c.category,preview_url:c.preview_url||null,preview_image:c.preview_image||null};if(i){let{error:t}=await x.from("templates").update(e).eq("id",i.id);if(t)throw t;N.oR.success("Template updated successfully")}else{let{error:t}=await x.from("templates").insert(e);if(t)throw t;N.oR.success("Template created successfully")}l()}catch(e){console.error("Error saving template:",e),N.oR.error(e.message||"Failed to save template")}finally{u(!1)}};return(0,a.jsx)(m,{open:t,onOpenChange:s,children:(0,a.jsxs)(h,{className:"max-w-md",children:[(0,a.jsx)(f,{children:(0,a.jsx)(g,{children:i?"Edit Template":"Add New Template"})}),(0,a.jsxs)("form",{onSubmit:p,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(v.J,{htmlFor:"title",children:"Title"}),(0,a.jsx)(j.p,{id:"title",value:c.title,onChange:e=>d({...c,title:e.target.value}),required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(v.J,{htmlFor:"description",children:"Description"}),(0,a.jsx)(b.T,{id:"description",value:c.description,onChange:e=>d({...c,description:e.target.value}),required:!0})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(v.J,{htmlFor:"price",children:"Price (₹)"}),(0,a.jsx)(j.p,{id:"price",type:"number",value:c.price,onChange:e=>d({...c,price:e.target.value}),required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(v.J,{htmlFor:"category",children:"Category"}),(0,a.jsxs)(w.l6,{value:c.category,onValueChange:e=>d({...c,category:e}),children:[(0,a.jsx)(w.bq,{children:(0,a.jsx)(w.yv,{placeholder:"Select category"})}),(0,a.jsx)(w.gC,{children:["Business","Portfolio","E-commerce","Blog","Marketing","Restaurant"].map(e=>(0,a.jsx)(w.eb,{value:e,children:e},e))})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(v.J,{htmlFor:"preview_url",children:"Preview URL"}),(0,a.jsx)(j.p,{id:"preview_url",type:"url",value:c.preview_url,onChange:e=>d({...c,preview_url:e.target.value}),placeholder:"https://example.com/preview"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(v.J,{htmlFor:"preview_image",children:"Preview Image URL"}),(0,a.jsx)(j.p,{id:"preview_image",type:"url",value:c.preview_image,onChange:e=>d({...c,preview_image:e.target.value}),placeholder:"https://example.com/image.jpg"})]}),(0,a.jsxs)("div",{className:"flex gap-2 pt-4",children:[(0,a.jsx)(n.$,{type:"button",variant:"outline",onClick:s,className:"flex-1",children:"Cancel"}),(0,a.jsx)(n.$,{type:"submit",disabled:o,className:"flex-1",children:o?"Saving...":i?"Update":"Create"})]})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[671,935,455,688,723,202,736,725,441,684,358],()=>t(283)),_N_E=e.O()}]);