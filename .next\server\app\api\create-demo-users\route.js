(()=>{var e={};e.id=913,e.ids=[913],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},52283:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>d,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>m});var t={};s.r(t),s.d(t,{POST:()=>n});var o=s(96559),a=s(48088),i=s(37719),u=s(32190),l=s(86345);async function n(e){try{let e=(0,l.UU)("https://aovrwjzhqrgbdhszdowg.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}}),r=[];for(let s of[{email:"<EMAIL>",password:"admin123",role:"admin",full_name:"Admin Demo"},{email:"<EMAIL>",password:"user123",role:"user",full_name:"User Demo"}])try{let{data:t,error:o}=await e.auth.admin.createUser({email:s.email,password:s.password,email_confirm:!0,user_metadata:{full_name:s.full_name}});if(o)if(o.message.includes("already registered")){let{data:t,error:o}=await e.auth.admin.listUsers();if(!o&&t){let o=t.users.find(e=>e.email===s.email);if(o){let{error:t}=await e.from("profiles").upsert({id:o.id,full_name:s.full_name,role:s.role,updated_at:new Date().toISOString()});t&&console.error(`Profile update error for ${s.email}:`,t),r.push({email:s.email,status:"updated",role:s.role})}else r.push({email:s.email,status:"error",error:"User exists but could not be found"})}else r.push({email:s.email,status:"error",error:"Could not list users"})}else console.error(`User creation error for ${s.email}:`,o),r.push({email:s.email,status:"error",error:o.message});else if(t.user){let{error:o}=await e.from("profiles").insert({id:t.user.id,full_name:s.full_name,role:s.role});o&&console.error(`Profile creation error for ${s.email}:`,o),r.push({email:s.email,status:"created",role:s.role})}}catch(e){console.error(`Error processing user ${s.email}:`,e),r.push({email:s.email,status:"error",error:e.message})}return u.NextResponse.json({success:!0,message:"Demo users processed successfully",results:r})}catch(e){return console.error("Demo user creation error:",e),u.NextResponse.json({success:!1,error:e.message||"Failed to create demo users"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/create-demo-users/route",pathname:"/api/create-demo-users",filename:"route",bundlePath:"app/api/create-demo-users/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\api\\create-demo-users\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:c,workUnitAsyncStorage:m,serverHooks:d}=p;function f(){return(0,i.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:m})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,580,345],()=>s(52283));module.exports=t})();