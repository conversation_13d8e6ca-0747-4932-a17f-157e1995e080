(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[700],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(5155);r(2115);var a=r(9708),n=r(2085),i=r(9434);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:r,size:n,asChild:d=!1,...l}=e,c=d?a.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:r,size:n,className:t})),...l})}},968:(e,t,r)=>{"use strict";r.d(t,{b:()=>o});var s=r(2115),a=r(3655),n=r(5155),i=s.forwardRef((e,t)=>(0,n.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=i},1572:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var s=r(5155),a=r(2115),n=r(5695),i=r(6695),o=r(285),d=r(2523),l=r(5057),c=r(2643),u=r(6671);let m=(0,r(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);var p=r(2919),h=r(8749),f=r(2657);function x(){let[e,t]=(0,a.useState)(""),[r,x]=(0,a.useState)(""),[g,v]=(0,a.useState)(!1),[b,w]=(0,a.useState)(!1),[y,j]=(0,a.useState)(!1),[N,k]=(0,a.useState)(!1),P=(0,n.useRouter)();(0,n.useSearchParams)();let S=(0,c.U)();(0,a.useEffect)(()=>{(async()=>{let{data:e,error:t}=await S.auth.getSession();t&&(console.error("Reset password session error:",t),u.oR.error("Invalid or expired reset link"),P.push("/forgot-password"))})()},[P,S]);let R=async t=>{if(t.preventDefault(),e!==r)return void u.oR.error("Passwords do not match");if(e.length<6)return void u.oR.error("Password must be at least 6 characters long");j(!0);try{let{error:t}=await S.auth.updateUser({password:e});if(t)throw t;k(!0),u.oR.success("Password updated successfully!"),setTimeout(()=>{P.push("/login")},2e3)}catch(e){console.error("Password update error:",e),u.oR.error(e.message||"Failed to update password")}finally{j(!1)}};return N?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4",children:(0,s.jsx)(i.Zp,{className:"w-full max-w-md shadow-xl",children:(0,s.jsxs)(i.aR,{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(m,{className:"h-8 w-8 text-green-600"})}),(0,s.jsx)(i.ZB,{className:"text-2xl font-bold",children:"Password Updated"}),(0,s.jsx)(i.BT,{children:"Your password has been successfully updated. You will be redirected to the login page."})]})})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4",children:(0,s.jsxs)(i.Zp,{className:"w-full max-w-md shadow-xl",children:[(0,s.jsxs)(i.aR,{className:"text-center",children:[(0,s.jsx)(i.ZB,{className:"text-2xl font-bold",children:"Reset Password"}),(0,s.jsx)(i.BT,{children:"Enter your new password below"})]}),(0,s.jsx)(i.Wu,{children:(0,s.jsxs)("form",{onSubmit:R,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"password",children:"New Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(d.p,{id:"password",type:g?"text":"password",placeholder:"Enter new password",value:e,onChange:e=>t(e.target.value),required:!0,minLength:6,className:"pl-10 pr-10"}),(0,s.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,s.jsx)("button",{type:"button",onClick:()=>v(!g),className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:g?(0,s.jsx)(h.A,{className:"h-4 w-4 text-muted-foreground"}):(0,s.jsx)(f.A,{className:"h-4 w-4 text-muted-foreground"})})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"confirmPassword",children:"Confirm New Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(d.p,{id:"confirmPassword",type:b?"text":"password",placeholder:"Confirm new password",value:r,onChange:e=>x(e.target.value),required:!0,minLength:6,className:"pl-10 pr-10"}),(0,s.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,s.jsx)("button",{type:"button",onClick:()=>w(!b),className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:b?(0,s.jsx)(h.A,{className:"h-4 w-4 text-muted-foreground"}):(0,s.jsx)(f.A,{className:"h-4 w-4 text-muted-foreground"})})]})]}),(0,s.jsxs)("div",{className:"text-xs text-muted-foreground space-y-1",children:[(0,s.jsx)("p",{children:"Password requirements:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,s.jsx)("li",{className:e.length>=6?"text-green-600":"",children:"At least 6 characters long"}),(0,s.jsx)("li",{className:e===r&&e?"text-green-600":"",children:"Passwords match"})]})]}),(0,s.jsx)(o.$,{type:"submit",className:"w-full",disabled:y||e!==r||e.length<6,children:y?(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Updating Password..."]}):"Update Password"})]})})]})})}},2523:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(5155);r(2115);var a=r(9434);function n(e){let{className:t,type:r,...n}=e;return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},2643:(e,t,r)=>{"use strict";r.d(t,{U:()=>a});var s=r(1935);function a(){let e="https://aovrwjzhqrgbdhszdowg.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFvdnJ3anpocXJnYmRoc3pkb3dnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4NTI5MjEsImV4cCI6MjA2NDQyODkyMX0.9TWCiuDturnqdOSlDeOWVroegkTM7Nra-W2LUoyGDSs";if(!e||!t)throw Error("Missing Supabase environment variables. Please check your .env.local file.");return(0,s.createBrowserClient)(e,t)}},2657:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2919:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},3655:(e,t,r)=>{"use strict";r.d(t,{hO:()=>d,sG:()=>o});var s=r(2115),a=r(7650),n=r(9708),i=r(5155),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,n.TL)(`Primitive.${t}`),a=s.forwardRef((e,s)=>{let{asChild:a,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(a?r:t,{...n,ref:s})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function d(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},5057:(e,t,r)=>{"use strict";r.d(t,{J:()=>i});var s=r(5155);r(2115);var a=r(968),n=r(9434);function i(e){let{className:t,...r}=e;return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},5695:(e,t,r)=>{"use strict";var s=r(8999);r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>n,aR:()=>i});var s=r(5155);r(2115);var a=r(9434);function n(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function i(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function o(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...r})}function d(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...r})}function l(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...r})}},8749:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(2596),a=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},9656:(e,t,r)=>{Promise.resolve().then(r.bind(r,1572))}},e=>{var t=t=>e(e.s=t);e.O(0,[671,935,455,441,684,358],()=>t(9656)),_N_E=e.O()}]);