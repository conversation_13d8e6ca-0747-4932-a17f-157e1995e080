"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[736],{64:(e,t,n)=>{n.d(t,{UC:()=>Q,B8:()=>H,bL:()=>z,l9:()=>J});var r=n(2115),o=n(5185),a=n(6081),i=n(7328),u=n(6101),l=n(1285),s=n(3655),c=n(9033),d=n(5845),f=n(4315),m=n(5155),p="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},b="RovingFocusGroup",[w,y,g]=(0,i.N)(b),[h,N]=(0,a.A)(b,[g]),[T,A]=h(b),I=r.forwardRef((e,t)=>(0,m.jsx)(w.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(w.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(R,{...e,ref:t})})}));I.displayName=b;var R=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:i=!1,dir:l,currentTabStopId:w,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:h,onEntryFocus:N,preventScrollOnEntryFocus:A=!1,...I}=e,R=r.useRef(null),x=(0,u.s)(t,R),E=(0,f.jH)(l),[D,C]=(0,d.i)({prop:w,defaultProp:null!=g?g:null,onChange:h,caller:b}),[M,j]=r.useState(!1),O=(0,c.c)(N),L=y(n),U=r.useRef(!1),[k,S]=r.useState(0);return r.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(p,O),()=>e.removeEventListener(p,O)},[O]),(0,m.jsx)(T,{scope:n,orientation:a,dir:E,loop:i,currentTabStopId:D,onItemFocus:r.useCallback(e=>C(e),[C]),onItemShiftTab:r.useCallback(()=>j(!0),[]),onFocusableItemAdd:r.useCallback(()=>S(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>S(e=>e-1),[]),children:(0,m.jsx)(s.sG.div,{tabIndex:M||0===k?-1:0,"data-orientation":a,...I,ref:x,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{U.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!U.current;if(e.target===e.currentTarget&&t&&!M){let t=new CustomEvent(p,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=L().filter(e=>e.focusable);F([e.find(e=>e.active),e.find(e=>e.id===D),...e].filter(Boolean).map(e=>e.ref.current),A)}}U.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>j(!1))})})}),x="RovingFocusGroupItem",E=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:a=!0,active:i=!1,tabStopId:u,children:c,...d}=e,f=(0,l.B)(),p=u||f,v=A(x,n),b=v.currentTabStopId===p,g=y(n),{onFocusableItemAdd:h,onFocusableItemRemove:N,currentTabStopId:T}=v;return r.useEffect(()=>{if(a)return h(),()=>N()},[a,h,N]),(0,m.jsx)(w.ItemSlot,{scope:n,id:p,focusable:a,active:i,children:(0,m.jsx)(s.sG.span,{tabIndex:b?0:-1,"data-orientation":v.orientation,...d,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?v.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(p)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void v.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return D[o]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=v.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>F(n))}}),children:"function"==typeof c?c({isCurrentTabStop:b,hasTabStop:null!=T}):c})})});E.displayName=x;var D={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function F(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var C=n(8905),M="Tabs",[j,O]=(0,a.A)(M,[N]),L=N(),[U,k]=j(M),S=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:o,defaultValue:a,orientation:i="horizontal",dir:u,activationMode:c="automatic",...p}=e,v=(0,f.jH)(u),[b,w]=(0,d.i)({prop:r,onChange:o,defaultProp:null!=a?a:"",caller:M});return(0,m.jsx)(U,{scope:n,baseId:(0,l.B)(),value:b,onValueChange:w,orientation:i,dir:v,activationMode:c,children:(0,m.jsx)(s.sG.div,{dir:v,"data-orientation":i,...p,ref:t})})});S.displayName=M;var _="TabsList",P=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...o}=e,a=k(_,n),i=L(n);return(0,m.jsx)(I,{asChild:!0,...i,orientation:a.orientation,dir:a.dir,loop:r,children:(0,m.jsx)(s.sG.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:t})})});P.displayName=_;var G="TabsTrigger",K=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:a=!1,...i}=e,u=k(G,n),l=L(n),c=W(u.baseId,r),d=q(u.baseId,r),f=r===u.value;return(0,m.jsx)(E,{asChild:!0,...l,focusable:!a,active:f,children:(0,m.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":d,"data-state":f?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:c,...i,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():u.onValueChange(r)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&u.onValueChange(r)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==u.activationMode;f||a||!e||u.onValueChange(r)})})})});K.displayName=G;var B="TabsContent",V=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:o,forceMount:a,children:i,...u}=e,l=k(B,n),c=W(l.baseId,o),d=q(l.baseId,o),f=o===l.value,p=r.useRef(f);return r.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,m.jsx)(C.C,{present:a||f,children:n=>{let{present:r}=n;return(0,m.jsx)(s.sG.div,{"data-state":f?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":c,hidden:!r,id:d,tabIndex:0,...u,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:r&&i})}})});function W(e,t){return"".concat(e,"-trigger-").concat(t)}function q(e,t){return"".concat(e,"-content-").concat(t)}V.displayName=B;var z=S,H=P,J=K,Q=V},8883:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},8905:(e,t,n)=>{n.d(t,{C:()=>i});var r=n(2115),o=n(6101),a=n(2712),i=e=>{let{present:t,children:n}=e,i=function(e){var t,n;let[o,i]=r.useState(),l=r.useRef(null),s=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=u(l.current);c.current="mounted"===d?e:"none"},[d]),(0,a.N)(()=>{let t=l.current,n=s.current;if(n!==e){let r=c.current,o=u(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,a.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=u(l.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(c.current=u(l.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{l.current=e?getComputedStyle(e):null,i(e)},[])}}(t),l="function"==typeof n?n({present:i.isPresent}):r.Children.only(n),s=(0,o.s)(i.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof n||i.isPresent?r.cloneElement(l,{ref:s}):null};function u(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"}}]);