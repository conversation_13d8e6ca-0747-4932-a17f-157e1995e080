(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{285:(e,s,r)=>{"use strict";r.d(s,{$:()=>d});var t=r(5155);r(2115);var a=r(9708),i=r(2085),n=r(9434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:s,variant:r,size:i,asChild:d=!1,...o}=e,c=d?a.DX:"button";return(0,t.jsx)(c,{"data-slot":"button",className:(0,n.cn)(l({variant:r,size:i,className:s})),...o})}},968:(e,s,r)=>{"use strict";r.d(s,{b:()=>l});var t=r(2115),a=r(3655),i=r(5155),n=t.forwardRef((e,s)=>(0,i.jsx)(a.sG.label,{...e,ref:s,onMouseDown:s=>{var r;s.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var l=n},2486:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},2523:(e,s,r)=>{"use strict";r.d(s,{p:()=>i});var t=r(5155);r(2115);var a=r(9434);function i(e){let{className:s,type:r,...i}=e;return(0,t.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...i})}},2643:(e,s,r)=>{"use strict";r.d(s,{U:()=>a});var t=r(1935);function a(){let e="https://aovrwjzhqrgbdhszdowg.supabase.co",s="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFvdnJ3anpocXJnYmRoc3pkb3dnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4NTI5MjEsImV4cCI6MjA2NDQyODkyMX0.9TWCiuDturnqdOSlDeOWVroegkTM7Nra-W2LUoyGDSs";if(!e||!s)throw Error("Missing Supabase environment variables. Please check your .env.local file.");return(0,t.createBrowserClient)(e,s)}},3047:(e,s,r)=>{Promise.resolve().then(r.bind(r,8868))},3655:(e,s,r)=>{"use strict";r.d(s,{hO:()=>d,sG:()=>l});var t=r(2115),a=r(7650),i=r(9708),n=r(5155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,s)=>{let r=(0,i.TL)(`Primitive.${s}`),a=t.forwardRef((e,t)=>{let{asChild:a,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(a?r:s,{...i,ref:t})});return a.displayName=`Primitive.${s}`,{...e,[s]:a}},{});function d(e,s){e&&a.flushSync(()=>e.dispatchEvent(s))}},4516:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},5057:(e,s,r)=>{"use strict";r.d(s,{J:()=>n});var t=r(5155);r(2115);var a=r(968),i=r(9434);function n(e){let{className:s,...r}=e;return(0,t.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...r})}},6695:(e,s,r)=>{"use strict";r.d(s,{BT:()=>d,Wu:()=>o,ZB:()=>l,Zp:()=>i,aR:()=>n});var t=r(5155);r(2115);var a=r(9434);function i(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...r})}function n(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...r})}function l(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",s),...r})}function d(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",s),...r})}function o(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",s),...r})}},8539:(e,s,r)=>{"use strict";r.d(s,{T:()=>i});var t=r(5155);r(2115);var a=r(9434);function i(e){let{className:s,...r}=e;return(0,t.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),...r})}},8868:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>g});var t=r(5155),a=r(2115),i=r(6695),n=r(285),l=r(2523),d=r(5057),o=r(8539),c=r(2486),u=r(8883),m=r(9420),x=r(4516);let h=(0,r(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var p=r(2643),f=r(6671);function g(){let[e,s]=(0,a.useState)({name:"",email:"",message:""}),[r,g]=(0,a.useState)(!1),v=(0,p.U)(),b=async r=>{if(r.preventDefault(),!e.name||!e.email||!e.message)return void f.oR.error("Please fill in all fields");g(!0);try{let{error:r}=await v.from("contact_requests").insert({name:e.name,email:e.email,message:e.message});if(r)throw r;f.oR.success("Message sent successfully! We will get back to you soon."),s({name:"",email:"",message:""})}catch(e){console.error("Error submitting contact form:",e),f.oR.error("Failed to send message. Please try again.")}finally{g(!1)}},j=e=>{s(s=>({...s,[e.target.name]:e.target.value}))};return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"text-center space-y-4",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Contact Us"}),(0,t.jsx)("p",{className:"text-xl text-muted-foreground max-w-2xl mx-auto",children:"Get in touch with our team. We're here to help you create amazing templates."})]}),(0,t.jsxs)("div",{className:"grid gap-8 md:grid-cols-2",children:[(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Send us a Message"}),(0,t.jsx)(i.BT,{children:"Fill out the form below and we'll get back to you as soon as possible"})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsxs)("form",{onSubmit:b,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.J,{htmlFor:"name",children:"Full Name"}),(0,t.jsx)(l.p,{id:"name",name:"name",placeholder:"Enter your full name",value:e.name,onChange:j,required:!0})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.J,{htmlFor:"email",children:"Email"}),(0,t.jsx)(l.p,{id:"email",name:"email",type:"email",placeholder:"Enter your email",value:e.email,onChange:j,required:!0})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.J,{htmlFor:"message",children:"Message"}),(0,t.jsx)(o.T,{id:"message",name:"message",placeholder:"Tell us more about your inquiry...",rows:6,value:e.message,onChange:j,required:!0})]}),(0,t.jsxs)(n.$,{type:"submit",className:"w-full",disabled:r,children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-2"}),r?"Sending...":"Send Message"]})]})})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Get in Touch"}),(0,t.jsx)(i.BT,{children:"Here are the different ways you can reach us"})]}),(0,t.jsxs)(i.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-10 h-10 bg-primary/10 rounded-lg",children:(0,t.jsx)(u.A,{className:"h-5 w-5 text-primary"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"Email"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"<EMAIL>"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-10 h-10 bg-primary/10 rounded-lg",children:(0,t.jsx)(m.A,{className:"h-5 w-5 text-primary"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"Phone"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"+****************"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-10 h-10 bg-primary/10 rounded-lg",children:(0,t.jsx)(x.A,{className:"h-5 w-5 text-primary"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"Address"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["123 Design Street",(0,t.jsx)("br",{}),"Creative City, CC 12345"]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-10 h-10 bg-primary/10 rounded-lg",children:(0,t.jsx)(h,{className:"h-5 w-5 text-primary"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"Business Hours"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Mon - Fri: 9:00 AM - 6:00 PM",(0,t.jsx)("br",{}),"Sat - Sun: 10:00 AM - 4:00 PM"]})]})]})]})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Frequently Asked Questions"}),(0,t.jsx)(i.BT,{children:"Quick answers to common questions"})]}),(0,t.jsxs)(i.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-1",children:"How do I customize a template?"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Use our live preview customizer to modify colors, fonts, layouts, and content in real-time."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-1",children:"Can I download the source code?"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Yes! You can export your customized templates as HTML/CSS or React components."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-1",children:"Do you offer custom development?"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"We offer custom template development services. Contact us to discuss your requirements."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-1",children:"What's your response time?"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"We typically respond to inquiries within 24 hours during business days."})]})]})]})]})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Find Us"}),(0,t.jsx)(i.BT,{children:"Visit our office or schedule a meeting"})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)("div",{className:"aspect-video bg-muted rounded-lg flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center text-muted-foreground",children:[(0,t.jsx)(x.A,{className:"h-12 w-12 mx-auto mb-2"}),(0,t.jsx)("p",{children:"Interactive Map Placeholder"}),(0,t.jsx)("p",{className:"text-sm",children:"123 Design Street, Creative City, CC 12345"})]})})})]})]})}},8883:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9420:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},9434:(e,s,r)=>{"use strict";r.d(s,{cn:()=>i});var t=r(2596),a=r(9688);function i(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,a.QP)((0,t.$)(s))}}},e=>{var s=s=>e(e.s=s);e.O(0,[671,935,455,441,684,358],()=>s(3047)),_N_E=e.O()}]);