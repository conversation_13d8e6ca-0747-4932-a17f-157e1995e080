import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  try {
    // Use service role key for admin operations
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const demoUsers = [
      {
        email: '<EMAIL>',
        password: 'admin123',
        role: 'admin',
        full_name: 'Admin Demo'
      },
      {
        email: '<EMAIL>',
        password: 'user123',
        role: 'user',
        full_name: 'User Demo'
      }
    ]

    const results = []

    for (const user of demoUsers) {
      try {
        // Try to create the user (will fail if already exists)
        const { data: newUser, error: createError } = await supabase.auth.admin.createUser({
          email: user.email,
          password: user.password,
          email_confirm: true,
          user_metadata: {
            full_name: user.full_name
          }
        })

        if (createError) {
          if (createError.message.includes('already registered')) {
            // User already exists, try to find and update profile
            const { data: users, error: listError } = await supabase.auth.admin.listUsers()

            if (!listError && users) {
              const existingUser = users.users.find(u => u.email === user.email)

              if (existingUser) {
                // Update profile
                const { error: profileError } = await supabase
                  .from('profiles')
                  .upsert({
                    id: existingUser.id,
                    full_name: user.full_name,
                    role: user.role,
                    updated_at: new Date().toISOString()
                  })

                if (profileError) {
                  console.error(`Profile update error for ${user.email}:`, profileError)
                }

                results.push({
                  email: user.email,
                  status: 'updated',
                  role: user.role
                })
              } else {
                results.push({
                  email: user.email,
                  status: 'error',
                  error: 'User exists but could not be found'
                })
              }
            } else {
              results.push({
                email: user.email,
                status: 'error',
                error: 'Could not list users'
              })
            }
          } else {
            console.error(`User creation error for ${user.email}:`, createError)
            results.push({
              email: user.email,
              status: 'error',
              error: createError.message
            })
          }
        } else if (newUser.user) {
          // User created successfully, create profile
          const { error: profileError } = await supabase
            .from('profiles')
            .insert({
              id: newUser.user.id,
              full_name: user.full_name,
              role: user.role
            })

          if (profileError) {
            console.error(`Profile creation error for ${user.email}:`, profileError)
          }

          results.push({
            email: user.email,
            status: 'created',
            role: user.role
          })
        }
      } catch (error: any) {
        console.error(`Error processing user ${user.email}:`, error)
        results.push({
          email: user.email,
          status: 'error',
          error: error.message
        })
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Demo users processed successfully',
      results
    })

  } catch (error: any) {
    console.error('Demo user creation error:', error)
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to create demo users'
      },
      { status: 500 }
    )
  }
}
