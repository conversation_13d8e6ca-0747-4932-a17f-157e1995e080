-- =====================================================
-- SAMPLE DATA FOR KALEIDONEX DATABASE
-- =====================================================

-- Insert sample categories
INSERT INTO categories (name, slug, description, icon, color) VALUES
('Web Templates', 'web-templates', 'Complete website templates', 'Globe', '#3B82F6'),
('Landing Pages', 'landing-pages', 'High-converting landing pages', 'Zap', '#10B981'),
('E-commerce', 'ecommerce', 'Online store templates', 'ShoppingCart', '#F59E0B'),
('Dashboards', 'dashboards', 'Admin and analytics dashboards', 'BarChart3', '#8B5CF6'),
('Mobile Apps', 'mobile-apps', 'Mobile application templates', 'Smartphone', '#EF4444'),
('Components', 'components', 'Reusable UI components', 'Package', '#06B6D4'),
('Portfolio', 'portfolio', 'Creative portfolio templates', 'User', '#EC4899'),
('Business', 'business', 'Professional business templates', 'Building', '#6366F1'),
('Blog', 'blog', 'Blog and content templates', 'FileText', '#84CC16'),
('SaaS', 'saas', 'Software as a Service templates', 'Cloud', '#F97316');

-- Insert sample tags
INSERT INTO tags (name, slug, description, color) VALUES
('React', 'react', 'Built with React framework', '#61DAFB'),
('Next.js', 'nextjs', 'Built with Next.js framework', '#000000'),
('TypeScript', 'typescript', 'Written in TypeScript', '#3178C6'),
('Tailwind CSS', 'tailwind-css', 'Styled with Tailwind CSS', '#06B6D4'),
('Responsive', 'responsive', 'Mobile-friendly design', '#10B981'),
('Dark Mode', 'dark-mode', 'Supports dark mode', '#374151'),
('Animation', 'animation', 'Includes animations', '#F59E0B'),
('SEO Optimized', 'seo-optimized', 'Search engine optimized', '#8B5CF6'),
('E-commerce', 'ecommerce-tag', 'E-commerce functionality', '#F59E0B'),
('Dashboard', 'dashboard-tag', 'Dashboard interface', '#8B5CF6'),
('Modern', 'modern', 'Modern design style', '#3B82F6'),
('Minimalist', 'minimalist', 'Clean and minimal design', '#6B7280'),
('Creative', 'creative', 'Creative and artistic design', '#EC4899'),
('Professional', 'professional', 'Professional business design', '#1F2937'),
('Startup', 'startup', 'Perfect for startups', '#10B981');

-- Insert sample templates
INSERT INTO templates (
  title, slug, description, long_description, price, original_price, discount_percentage,
  category_id, preview_image, preview_url, demo_url, features, tech_stack, 
  difficulty_level, is_featured, is_active, is_premium
) VALUES
(
  'Modern Business Landing Page',
  'modern-business-landing',
  'A sleek and professional landing page perfect for businesses',
  'This modern business landing page template features a clean design with smooth animations, responsive layout, and conversion-optimized sections. Perfect for startups, agencies, and professional services. Includes contact forms, testimonials, pricing tables, and more.',
  2999, 3999, 25,
  (SELECT id FROM categories WHERE slug = 'landing-pages'),
  'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800',
  'https://demo.kaleidonex.com/modern-business',
  'https://demo.kaleidonex.com/modern-business',
  ARRAY['Responsive Design', 'Dark Mode', 'Contact Forms', 'SEO Optimized', 'Animation Effects'],
  ARRAY['Next.js', 'TypeScript', 'Tailwind CSS', 'Framer Motion'],
  'beginner',
  true, true, false
),
(
  'E-commerce Dashboard Pro',
  'ecommerce-dashboard-pro',
  'Complete admin dashboard for e-commerce management',
  'A comprehensive e-commerce dashboard with analytics, product management, order tracking, and customer management features. Built with modern technologies and best practices. Includes charts, tables, forms, and real-time data visualization.',
  4999, 6999, 29,
  (SELECT id FROM categories WHERE slug = 'dashboards'),
  'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800',
  'https://demo.kaleidonex.com/ecommerce-dashboard',
  'https://demo.kaleidonex.com/ecommerce-dashboard',
  ARRAY['Analytics Charts', 'Product Management', 'Order Tracking', 'User Management', 'Real-time Data'],
  ARRAY['React', 'TypeScript', 'Tailwind CSS', 'Chart.js', 'Recharts'],
  'intermediate',
  true, true, true
),
(
  'Creative Portfolio Showcase',
  'creative-portfolio-showcase',
  'Stunning portfolio template for creative professionals',
  'Showcase your creative work with this beautiful portfolio template. Features smooth animations, image galleries, project showcases, and contact forms. Perfect for designers, photographers, artists, and creative agencies.',
  1999, 2499, 20,
  (SELECT id FROM categories WHERE slug = 'portfolio'),
  'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800',
  'https://demo.kaleidonex.com/creative-portfolio',
  'https://demo.kaleidonex.com/creative-portfolio',
  ARRAY['Image Gallery', 'Project Showcase', 'Contact Forms', 'Smooth Animations', 'Responsive Design'],
  ARRAY['Next.js', 'TypeScript', 'Tailwind CSS', 'Framer Motion'],
  'beginner',
  false, true, false
),
(
  'SaaS Landing Page Ultimate',
  'saas-landing-ultimate',
  'High-converting SaaS landing page with pricing tables',
  'The ultimate SaaS landing page template with everything you need to convert visitors into customers. Includes hero sections, feature highlights, pricing tables, testimonials, FAQ sections, and more.',
  3499, 4999, 30,
  (SELECT id FROM categories WHERE slug = 'saas'),
  'https://images.unsplash.com/photo-1551434678-e076c223a692?w=800',
  'https://demo.kaleidonex.com/saas-landing',
  'https://demo.kaleidonex.com/saas-landing',
  ARRAY['Pricing Tables', 'Feature Highlights', 'Testimonials', 'FAQ Section', 'CTA Optimization'],
  ARRAY['Next.js', 'TypeScript', 'Tailwind CSS', 'Stripe Integration'],
  'intermediate',
  true, true, true
),
(
  'Blog & Magazine Template',
  'blog-magazine-template',
  'Perfect template for blogs, magazines, and content websites',
  'A beautiful blog and magazine template with multiple layouts, category pages, article pages, and author profiles. Optimized for content creators and publishers.',
  999, 1499, 33,
  (SELECT id FROM categories WHERE slug = 'blog'),
  'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800',
  'https://demo.kaleidonex.com/blog-magazine',
  'https://demo.kaleidonex.com/blog-magazine',
  ARRAY['Multiple Layouts', 'Category Pages', 'Author Profiles', 'Search Functionality', 'Social Sharing'],
  ARRAY['Next.js', 'TypeScript', 'Tailwind CSS', 'MDX'],
  'beginner',
  false, true, false
),
(
  'Restaurant & Food Template',
  'restaurant-food-template',
  'Elegant restaurant template with menu and reservation system',
  'A beautiful restaurant template featuring menu showcases, reservation systems, gallery sections, and contact information. Perfect for restaurants, cafes, and food businesses.',
  2499, 2999, 17,
  (SELECT id FROM categories WHERE slug = 'business'),
  'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=800',
  'https://demo.kaleidonex.com/restaurant',
  'https://demo.kaleidonex.com/restaurant',
  ARRAY['Menu Showcase', 'Reservation System', 'Image Gallery', 'Contact Forms', 'Location Maps'],
  ARRAY['Next.js', 'TypeScript', 'Tailwind CSS', 'Google Maps API'],
  'beginner',
  false, true, false
);

-- Insert sample app settings
INSERT INTO app_settings (key, value, description, type, is_public) VALUES
('site_name', '"KaleidoneX"', 'Website name', 'string', true),
('site_description', '"Create stunning, customizable templates with our powerful design tools"', 'Website description', 'string', true),
('contact_email', '"<EMAIL>"', 'Contact email address', 'string', true),
('support_phone', '"+91-9876543210"', 'Support phone number', 'string', true),
('company_address', '"123 Tech Street, Innovation City, IN 12345"', 'Company address', 'string', true),
('max_file_size', '52428800', 'Maximum file upload size in bytes (50MB)', 'number', false),
('allowed_file_types', '["jpg", "jpeg", "png", "gif", "pdf", "zip", "rar"]', 'Allowed file types for upload', 'array', false),
('payment_gateway', '"razorpay"', 'Default payment gateway', 'string', false),
('currency', '"INR"', 'Default currency', 'string', true),
('currency_symbol', '"₹"', 'Currency symbol', 'string', true),
('tax_rate', '18', 'Tax rate percentage', 'number', false),
('free_download_limit', '3', 'Free downloads per user', 'number', false),
('maintenance_mode', 'false', 'Enable maintenance mode', 'boolean', false),
('enable_reviews', 'true', 'Enable template reviews', 'boolean', true),
('enable_favorites', 'true', 'Enable favorites/wishlist', 'boolean', true),
('enable_notifications', 'true', 'Enable user notifications', 'boolean', true),
('social_facebook', '"https://facebook.com/kaleidonex"', 'Facebook page URL', 'string', true),
('social_twitter', '"https://twitter.com/kaleidonex"', 'Twitter profile URL', 'string', true),
('social_instagram', '"https://instagram.com/kaleidonex"', 'Instagram profile URL', 'string', true),
('social_linkedin', '"https://linkedin.com/company/kaleidonex"', 'LinkedIn page URL', 'string', true),
('analytics_google', '""', 'Google Analytics tracking ID', 'string', false),
('seo_keywords', '"templates, web design, ui components, dashboard, landing pages"', 'Default SEO keywords', 'string', true);

-- Insert sample FAQs
INSERT INTO faqs (question, answer, category, sort_order, is_featured) VALUES
('How do I purchase a template?', 'Browse our template gallery, click on a template you like, and click the "Buy Now" button. You''ll be redirected to our secure payment gateway powered by Razorpay.', 'purchasing', 1, true),
('What payment methods do you accept?', 'We accept all major credit cards, debit cards, UPI, net banking, and digital wallets through Razorpay. All payments are secure and encrypted.', 'payment', 2, true),
('Can I customize the templates?', 'Yes! All our templates come with extensive customization options. You can modify colors, fonts, layouts, and content using our built-in customization tools.', 'customization', 3, true),
('Do you offer refunds?', 'We offer a 30-day money-back guarantee if you''re not satisfied with your purchase. Please contact our support team for refund requests.', 'refunds', 4, true),
('How do I download my purchased templates?', 'After successful payment, you''ll receive a download link via email. You can also access your purchases from your account dashboard.', 'downloading', 5, true),
('Are the templates responsive?', 'Yes, all our templates are fully responsive and work perfectly on desktop, tablet, and mobile devices.', 'technical', 6, false),
('Do you provide support?', 'Yes, we provide comprehensive support for all our templates. You can contact us through our support system or email.', 'support', 7, false),
('Can I use templates for commercial projects?', 'Yes, our standard license allows you to use templates for commercial projects. Check the license details for specific terms.', 'licensing', 8, false),
('How often do you add new templates?', 'We add new templates regularly, typically 2-3 new templates every month. Subscribe to our newsletter to stay updated.', 'general', 9, false),
('Do you offer custom development?', 'Yes, we offer custom development services for specific requirements. Contact our sales team for custom project quotes.', 'services', 10, false);

-- Insert sample coupons
INSERT INTO coupons (code, name, description, type, value, minimum_amount, usage_limit, is_active, starts_at, expires_at) VALUES
('WELCOME20', 'Welcome Discount', 'Get 20% off on your first purchase', 'percentage', 20, 1000, 100, true, NOW(), NOW() + INTERVAL '30 days'),
('SAVE500', 'Flat 500 Off', 'Get flat ₹500 off on orders above ₹2500', 'fixed_amount', 500, 2500, 50, true, NOW(), NOW() + INTERVAL '15 days'),
('PREMIUM30', 'Premium Templates Discount', 'Get 30% off on all premium templates', 'percentage', 30, 3000, 25, true, NOW(), NOW() + INTERVAL '7 days'),
('BULK15', 'Bulk Purchase Discount', 'Get 15% off when you buy 3 or more templates', 'percentage', 15, 5000, 20, true, NOW(), NOW() + INTERVAL '60 days');

-- Link templates with tags (many-to-many relationship)
INSERT INTO template_tags (template_id, tag_id) VALUES
-- Modern Business Landing Page
((SELECT id FROM templates WHERE slug = 'modern-business-landing'), (SELECT id FROM tags WHERE slug = 'nextjs')),
((SELECT id FROM templates WHERE slug = 'modern-business-landing'), (SELECT id FROM tags WHERE slug = 'typescript')),
((SELECT id FROM templates WHERE slug = 'modern-business-landing'), (SELECT id FROM tags WHERE slug = 'tailwind-css')),
((SELECT id FROM templates WHERE slug = 'modern-business-landing'), (SELECT id FROM tags WHERE slug = 'responsive')),
((SELECT id FROM templates WHERE slug = 'modern-business-landing'), (SELECT id FROM tags WHERE slug = 'seo-optimized')),
((SELECT id FROM templates WHERE slug = 'modern-business-landing'), (SELECT id FROM tags WHERE slug = 'professional')),

-- E-commerce Dashboard Pro
((SELECT id FROM templates WHERE slug = 'ecommerce-dashboard-pro'), (SELECT id FROM tags WHERE slug = 'react')),
((SELECT id FROM templates WHERE slug = 'ecommerce-dashboard-pro'), (SELECT id FROM tags WHERE slug = 'typescript')),
((SELECT id FROM templates WHERE slug = 'ecommerce-dashboard-pro'), (SELECT id FROM tags WHERE slug = 'tailwind-css')),
((SELECT id FROM templates WHERE slug = 'ecommerce-dashboard-pro'), (SELECT id FROM tags WHERE slug = 'dashboard-tag')),
((SELECT id FROM templates WHERE slug = 'ecommerce-dashboard-pro'), (SELECT id FROM tags WHERE slug = 'ecommerce-tag')),

-- Creative Portfolio Showcase
((SELECT id FROM templates WHERE slug = 'creative-portfolio-showcase'), (SELECT id FROM tags WHERE slug = 'nextjs')),
((SELECT id FROM templates WHERE slug = 'creative-portfolio-showcase'), (SELECT id FROM tags WHERE slug = 'typescript')),
((SELECT id FROM templates WHERE slug = 'creative-portfolio-showcase'), (SELECT id FROM tags WHERE slug = 'tailwind-css')),
((SELECT id FROM templates WHERE slug = 'creative-portfolio-showcase'), (SELECT id FROM tags WHERE slug = 'animation')),
((SELECT id FROM templates WHERE slug = 'creative-portfolio-showcase'), (SELECT id FROM tags WHERE slug = 'creative')),

-- SaaS Landing Page Ultimate
((SELECT id FROM templates WHERE slug = 'saas-landing-ultimate'), (SELECT id FROM tags WHERE slug = 'nextjs')),
((SELECT id FROM templates WHERE slug = 'saas-landing-ultimate'), (SELECT id FROM tags WHERE slug = 'typescript')),
((SELECT id FROM templates WHERE slug = 'saas-landing-ultimate'), (SELECT id FROM tags WHERE slug = 'tailwind-css')),
((SELECT id FROM templates WHERE slug = 'saas-landing-ultimate'), (SELECT id FROM tags WHERE slug = 'startup')),
((SELECT id FROM templates WHERE slug = 'saas-landing-ultimate'), (SELECT id FROM tags WHERE slug = 'modern')),

-- Blog & Magazine Template
((SELECT id FROM templates WHERE slug = 'blog-magazine-template'), (SELECT id FROM tags WHERE slug = 'nextjs')),
((SELECT id FROM templates WHERE slug = 'blog-magazine-template'), (SELECT id FROM tags WHERE slug = 'typescript')),
((SELECT id FROM templates WHERE slug = 'blog-magazine-template'), (SELECT id FROM tags WHERE slug = 'tailwind-css')),
((SELECT id FROM templates WHERE slug = 'blog-magazine-template'), (SELECT id FROM tags WHERE slug = 'seo-optimized')),
((SELECT id FROM templates WHERE slug = 'blog-magazine-template'), (SELECT id FROM tags WHERE slug = 'minimalist')),

-- Restaurant & Food Template
((SELECT id FROM templates WHERE slug = 'restaurant-food-template'), (SELECT id FROM tags WHERE slug = 'nextjs')),
((SELECT id FROM templates WHERE slug = 'restaurant-food-template'), (SELECT id FROM tags WHERE slug = 'typescript')),
((SELECT id FROM templates WHERE slug = 'restaurant-food-template'), (SELECT id FROM tags WHERE slug = 'tailwind-css')),
((SELECT id FROM templates WHERE slug = 'restaurant-food-template'), (SELECT id FROM tags WHERE slug = 'responsive')),
((SELECT id FROM templates WHERE slug = 'restaurant-food-template'), (SELECT id FROM tags WHERE slug = 'professional'));
