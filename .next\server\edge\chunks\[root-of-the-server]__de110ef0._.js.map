{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { type NextRequest, NextResponse } from 'next/server'\nimport { createServerClient } from '@supabase/ssr'\n\nexport async function middleware(request: NextRequest) {\n  let supabaseResponse = NextResponse.next({\n    request,\n  })\n\n  const supabase = createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return request.cookies.getAll()\n        },\n        setAll(cookiesToSet) {\n          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value))\n          supabaseResponse = NextResponse.next({\n            request,\n          })\n          cookiesToSet.forEach(({ name, value, options }) =>\n            supabaseResponse.cookies.set(name, value, options)\n          )\n        },\n      },\n    }\n  )\n\n  // Get user session\n  const { data: { user }, error } = await supabase.auth.getUser()\n\n  // Handle admin route protection\n  if (request.nextUrl.pathname.startsWith('/admin')) {\n    if (error || !user) {\n      // Not authenticated - redirect to login\n      const url = request.nextUrl.clone()\n      url.pathname = '/login'\n      url.searchParams.set('redirectTo', request.nextUrl.pathname)\n      return NextResponse.redirect(url)\n    }\n\n    // Check if user is admin\n    const { data: profile, error: profileError } = await supabase\n      .from('profiles')\n      .select('role')\n      .eq('id', user.id)\n      .single()\n\n    if (profileError || !profile || profile.role !== 'admin') {\n      // Not admin - redirect to 404\n      const url = request.nextUrl.clone()\n      url.pathname = '/404'\n      return NextResponse.redirect(url)\n    }\n  }\n\n  // Handle dashboard route protection (requires authentication)\n  if (request.nextUrl.pathname.startsWith('/dashboard')) {\n    if (error || !user) {\n      const url = request.nextUrl.clone()\n      url.pathname = '/login'\n      url.searchParams.set('redirectTo', request.nextUrl.pathname)\n      return NextResponse.redirect(url)\n    }\n  }\n\n  return supabaseResponse\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * Feel free to modify this pattern to include more paths.\n     */\n    '/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AAAA;;;AAEO,eAAe,WAAW,OAAoB;IACnD,IAAI,mBAAmB,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvC;IACF;IAEA,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,qBAAkB,AAAD,sUAGhC;QACE,SAAS;YACP;gBACE,OAAO,QAAQ,OAAO,CAAC,MAAM;YAC/B;YACA,QAAO,YAAY;gBACjB,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAAK,QAAQ,OAAO,CAAC,GAAG,CAAC,MAAM;gBAC7E,mBAAmB,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACnC;gBACF;gBACA,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,iBAAiB,OAAO,CAAC,GAAG,CAAC,MAAM,OAAO;YAE9C;QACF;IACF;IAGF,mBAAmB;IACnB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE7D,gCAAgC;IAChC,IAAI,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW;QACjD,IAAI,SAAS,CAAC,MAAM;YAClB,wCAAwC;YACxC,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;YACjC,IAAI,QAAQ,GAAG;YACf,IAAI,YAAY,CAAC,GAAG,CAAC,cAAc,QAAQ,OAAO,CAAC,QAAQ;YAC3D,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QAEA,yBAAyB;QACzB,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,YACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,IAAI,gBAAgB,CAAC,WAAW,QAAQ,IAAI,KAAK,SAAS;YACxD,8BAA8B;YAC9B,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;YACjC,IAAI,QAAQ,GAAG;YACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;IACF;IAEA,8DAA8D;IAC9D,IAAI,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,eAAe;QACrD,IAAI,SAAS,CAAC,MAAM;YAClB,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;YACjC,IAAI,QAAQ,GAAG;YACf,IAAI,YAAY,CAAC,GAAG,CAAC,cAAc,QAAQ,OAAO,CAAC,QAAQ;YAC3D,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;IACF;IAEA,OAAO;AACT;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}