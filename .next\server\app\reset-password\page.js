(()=>{var e={};e.id=700,e.ids=[700],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12597:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20032:(e,t,r)=>{Promise.resolve().then(r.bind(r,22723))},22723:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var s=r(60687),a=r(43210),o=r(16189),n=r(44493),i=r(29523),d=r(89667),l=r(54300),c=r(79481),u=r(52581);let p=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);var x=r(64021),m=r(12597),h=r(13861);function f(){let[e,t]=(0,a.useState)(""),[r,f]=(0,a.useState)(""),[g,w]=(0,a.useState)(!1),[b,v]=(0,a.useState)(!1),[y,j]=(0,a.useState)(!1),[N,P]=(0,a.useState)(!1),k=(0,o.useRouter)();(0,o.useSearchParams)();let q=(0,c.U)(),A=async t=>{if(t.preventDefault(),e!==r)return void u.oR.error("Passwords do not match");if(e.length<6)return void u.oR.error("Password must be at least 6 characters long");j(!0);try{let{error:t}=await q.auth.updateUser({password:e});if(t)throw t;P(!0),u.oR.success("Password updated successfully!"),setTimeout(()=>{k.push("/login")},2e3)}catch(e){console.error("Password update error:",e),u.oR.error(e.message||"Failed to update password")}finally{j(!1)}};return N?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4",children:(0,s.jsx)(n.Zp,{className:"w-full max-w-md shadow-xl",children:(0,s.jsxs)(n.aR,{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(p,{className:"h-8 w-8 text-green-600"})}),(0,s.jsx)(n.ZB,{className:"text-2xl font-bold",children:"Password Updated"}),(0,s.jsx)(n.BT,{children:"Your password has been successfully updated. You will be redirected to the login page."})]})})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4",children:(0,s.jsxs)(n.Zp,{className:"w-full max-w-md shadow-xl",children:[(0,s.jsxs)(n.aR,{className:"text-center",children:[(0,s.jsx)(n.ZB,{className:"text-2xl font-bold",children:"Reset Password"}),(0,s.jsx)(n.BT,{children:"Enter your new password below"})]}),(0,s.jsx)(n.Wu,{children:(0,s.jsxs)("form",{onSubmit:A,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"password",children:"New Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(d.p,{id:"password",type:g?"text":"password",placeholder:"Enter new password",value:e,onChange:e=>t(e.target.value),required:!0,minLength:6,className:"pl-10 pr-10"}),(0,s.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,s.jsx)("button",{type:"button",onClick:()=>w(!g),className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:g?(0,s.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"}):(0,s.jsx)(h.A,{className:"h-4 w-4 text-muted-foreground"})})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"confirmPassword",children:"Confirm New Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(d.p,{id:"confirmPassword",type:b?"text":"password",placeholder:"Confirm new password",value:r,onChange:e=>f(e.target.value),required:!0,minLength:6,className:"pl-10 pr-10"}),(0,s.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,s.jsx)("button",{type:"button",onClick:()=>v(!b),className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:b?(0,s.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"}):(0,s.jsx)(h.A,{className:"h-4 w-4 text-muted-foreground"})})]})]}),(0,s.jsxs)("div",{className:"text-xs text-muted-foreground space-y-1",children:[(0,s.jsx)("p",{children:"Password requirements:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,s.jsx)("li",{className:e.length>=6?"text-green-600":"",children:"At least 6 characters long"}),(0,s.jsx)("li",{className:e===r&&e?"text-green-600":"",children:"Passwords match"})]})]}),(0,s.jsx)(i.$,{type:"submit",className:"w-full",disabled:y||e!==r||e.length<6,children:y?(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Updating Password..."]}):"Update Password"})]})})]})})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33184:(e,t,r)=>{Promise.resolve().then(r.bind(r,85316))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>i,Zp:()=>o,aR:()=>n});var s=r(60687);r(43210);var a=r(4780);function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},54300:(e,t,r)=>{"use strict";r.d(t,{J:()=>d});var s=r(60687),a=r(43210),o=r(14163),n=a.forwardRef((e,t)=>(0,s.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var i=r(4780);function d({className:e,...t}){return(0,s.jsx)(n,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56706:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=r(65239),a=r(48088),o=r(88170),n=r.n(o),i=r(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(t,d);let l={children:["",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,85316)),"C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\reset-password\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\reset-password\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/reset-password/page",pathname:"/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64021:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85316:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\kaleidonex\\\\src\\\\app\\\\reset-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\reset-password\\page.tsx","default")},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>o});var s=r(60687);r(43210);var a=r(4780);function o({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,529,658,391],()=>r(56706));module.exports=s})();