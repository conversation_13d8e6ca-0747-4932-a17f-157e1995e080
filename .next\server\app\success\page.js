(()=>{var e={};e.id=582,e.ids=[582],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(e,r,s)=>{"use strict";s.d(r,{cn:()=>i});var t=s(75986),a=s(8974);function i(...e){return(0,a.QP)((0,t.$)(e))}},11997:e=>{"use strict";e.exports=require("punycode")},16513:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,85814,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23469:(e,r,s)=>{"use strict";s.d(r,{$:()=>d});var t=s(37413);s(61120);var a=s(70403),i=s(50662),n=s(10974);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:r,size:s,asChild:i=!1,...d}){let c=i?a.DX:"button";return(0,t.jsx)(c,{"data-slot":"button",className:(0,n.cn)(o({variant:r,size:s,className:e})),...d})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29968:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>x});var t=s(37413),a=s(78963),i=s(23469),n=s(91142),o=s(26373);let d=(0,o.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]),c=(0,o.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var u=s(36440),l=s(4536),p=s.n(l);function x(){return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,t.jsxs)(a.Zp,{className:"w-full max-w-md",children:[(0,t.jsxs)(a.aR,{className:"text-center",children:[(0,t.jsx)("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100",children:(0,t.jsx)(n.A,{className:"h-6 w-6 text-green-600"})}),(0,t.jsx)(a.ZB,{className:"text-2xl",children:"Payment Successful!"}),(0,t.jsx)(a.BT,{children:"Your template purchase has been completed successfully"})]}),(0,t.jsxs)(a.Wu,{className:"space-y-4",children:[(0,t.jsx)("div",{className:"text-center space-y-2",children:(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Thank you for your purchase! You can now access your template from your dashboard."})}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p(),{href:"/dashboard",children:(0,t.jsxs)(i.$,{className:"w-full",children:[(0,t.jsx)(d,{className:"h-4 w-4 mr-2"}),"Go to Dashboard"]})}),(0,t.jsx)(p(),{href:"/templates",children:(0,t.jsxs)(i.$,{variant:"outline",className:"w-full",children:[(0,t.jsx)(c,{className:"h-4 w-4 mr-2"}),"Browse More Templates"]})}),(0,t.jsx)(p(),{href:"/",children:(0,t.jsxs)(i.$,{variant:"ghost",className:"w-full",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Back to Home"]})})]})]})]})})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36440:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(26373).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},44648:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>l,pages:()=>u,routeModule:()=>p,tree:()=>c});var t=s(65239),a=s(48088),i=s(88170),n=s.n(i),o=s(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);s.d(r,d);let c={children:["",{children:["success",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,29968)),"C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\success\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\success\\page.tsx"],l={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/success/page",pathname:"/success",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},78963:(e,r,s)=>{"use strict";s.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>i,aR:()=>n});var t=s(37413);s(61120);var a=s(10974);function i({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function n({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function o({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...r})}function d({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...r})}function c({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...r})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80065:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,4536,23))},81630:e=>{"use strict";e.exports=require("http")},91142:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(26373).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,529,658,640,391],()=>s(44648));module.exports=t})();