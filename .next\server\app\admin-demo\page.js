(()=>{var e={};e.id=78,e.ids=[78],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8386:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>u,tree:()=>c});var a=t(65239),r=t(48088),i=t(88170),n=t.n(i),l=t(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let c={children:["",{children:["admin-demo",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,43298)),"C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\admin-demo\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\admin-demo\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin-demo/page",pathname:"/admin-demo",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23094:(e,s,t)=>{Promise.resolve().then(t.bind(t,60588))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},41312:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},43298:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\kaleidonex\\\\src\\\\app\\\\admin-demo\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Documents\\kaleidonex\\src\\app\\admin-demo\\page.tsx","default")},54220:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60046:(e,s,t)=>{Promise.resolve().then(t.bind(t,43298))},60588:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var a=t(60687),r=t(43210),i=t(44493),n=t(29523),l=t(96834),d=t(56770),c=t(52581),o=t(41550),m=t(71057),u=t(84027),x=t(41312),h=t(31158),p=t(54220),j=t(88233);let f=[{id:"1",name:"John Doe",email:"<EMAIL>",message:"I'm interested in your premium templates. Can you provide more information about pricing?",created_at:"2024-01-15T10:30:00Z"},{id:"2",name:"Jane Smith",email:"<EMAIL>",message:"I need a custom template for my e-commerce business. Please contact me.",created_at:"2024-01-14T15:45:00Z"}],v=[{id:"1",user_id:"user1",template_id:"template1",amount:2999,currency:"INR",razorpay_payment_id:"pay_123456789",status:"completed",created_at:"2024-01-15T12:00:00Z",templates:{title:"Modern Business Template",category:"Business"},profiles:{full_name:"John Doe"}},{id:"2",user_id:"user2",template_id:"template2",amount:1999,currency:"INR",razorpay_payment_id:"pay_987654321",status:"completed",created_at:"2024-01-14T09:30:00Z",templates:{title:"Creative Portfolio",category:"Portfolio"},profiles:{full_name:"Jane Smith"}}],g=[{id:"1",user_id:"user1",navbar_style:"modern",hero_section:"hero1",footer_style:"simple",created_at:"2024-01-15T14:20:00Z",updated_at:"2024-01-15T14:20:00Z",profiles:{full_name:"John Doe"}},{id:"2",user_id:"user2",navbar_style:"classic",hero_section:"hero2",footer_style:"detailed",created_at:"2024-01-14T11:15:00Z",updated_at:"2024-01-14T11:15:00Z",profiles:{full_name:"Jane Smith"}}],N=[{id:"1",ip_address:"*************",path:"/",user_agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",created_at:"2024-01-15T16:30:00Z"},{id:"2",ip_address:"*************",path:"/templates",user_agent:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",created_at:"2024-01-15T16:25:00Z"},{id:"3",ip_address:"*************",path:"/customize",user_agent:"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36",created_at:"2024-01-15T16:20:00Z"}];function y(){let[e,s]=(0,r.useState)("contacts"),t=(e,s,t)=>{let a=new Blob([[t,...e.map(e=>t.map(s=>{switch(s){case"Name":return e.name||e.profiles?.full_name||"Unknown";case"Email":return e.email||"N/A";case"Message":return e.message||"N/A";case"User Email":case"User":return e.profiles?.full_name||"Unknown";case"Template":return e.templates?.title||"N/A";case"Amount":return e.amount?.toString()||"N/A";case"Currency":return e.currency||"N/A";case"Status":return e.status||"N/A";case"Payment ID":return e.razorpay_payment_id||"N/A";case"Navbar Style":return e.navbar_style||"N/A";case"Hero Section":return e.hero_section||"N/A";case"Footer Style":return e.footer_style||"N/A";case"IP Address":return e.ip_address||"Unknown";case"Path":return e.path||"N/A";case"User Agent":return e.user_agent||"Unknown";case"Created At":return new Date(e.created_at).toLocaleString();case"Updated At":return new Date(e.updated_at||e.created_at).toLocaleString();default:return"N/A"}}))].map(e=>e.map(e=>`"${e}"`).join(",")).join("\n")],{type:"text/csv"}),r=window.URL.createObjectURL(a),i=document.createElement("a");i.href=r,i.download=`${s}-${new Date().toISOString().split("T")[0]}.csv`,i.click(),window.URL.revokeObjectURL(r),c.oR.success(`${s} exported successfully!`)},y=(e,s)=>{c.oR.success(`${s} deleted successfully! (Demo mode)`)};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Admin Panel (Demo)"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"This is a demo of the admin panel functionality with sample data"})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Contact Requests"}),(0,a.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:f.length})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Total Purchases"}),(0,a.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:v.length})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Customizations"}),(0,a.jsx)(u.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:g.length})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Visitor Logs"}),(0,a.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:N.length})})]})]}),(0,a.jsxs)(d.tU,{value:e,onValueChange:s,children:[(0,a.jsxs)(d.j7,{className:"grid w-full grid-cols-4",children:[(0,a.jsx)(d.Xi,{value:"contacts",children:"Contact Requests"}),(0,a.jsx)(d.Xi,{value:"purchases",children:"Purchases"}),(0,a.jsx)(d.Xi,{value:"customizations",children:"Customizations"}),(0,a.jsx)(d.Xi,{value:"visitors",children:"Visitor Logs"})]}),(0,a.jsx)(d.av,{value:"contacts",className:"space-y-4",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(i.ZB,{children:"Contact Requests"}),(0,a.jsx)(i.BT,{children:"Manage customer inquiries and messages"})]}),(0,a.jsx)("div",{className:"flex gap-2",children:(0,a.jsxs)(n.$,{variant:"outline",onClick:()=>t(f,"contact-requests",["Name","Email","Message","Created At"]),children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Export CSV"]})})]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:f.map(e=>(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.email})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.$,{variant:"outline",size:"sm",children:(0,a.jsx)(p.A,{className:"h-4 w-4"})}),(0,a.jsx)(n.$,{variant:"destructive",size:"sm",onClick:()=>y(e.id,"Contact request"),children:(0,a.jsx)(j.A,{className:"h-4 w-4"})})]})]}),(0,a.jsx)("p",{className:"text-sm mb-2",children:e.message}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:new Date(e.created_at).toLocaleString()})]},e.id))})})]})}),(0,a.jsx)(d.av,{value:"purchases",className:"space-y-4",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(i.ZB,{children:"Purchases"}),(0,a.jsxs)(i.BT,{children:["Total Revenue: ₹",v.reduce((e,s)=>e+s.amount,0)," • ",v.length," purchases"]})]}),(0,a.jsx)("div",{className:"flex gap-2",children:(0,a.jsxs)(n.$,{variant:"outline",onClick:()=>t(v,"purchases",["User Email","Template","Amount","Currency","Status","Payment ID","Created At"]),children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Export CSV"]})})]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:v.map(e=>(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold",children:e.templates.title}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.profiles.full_name})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.$,{variant:"outline",size:"sm",children:(0,a.jsx)(p.A,{className:"h-4 w-4"})}),(0,a.jsx)(n.$,{variant:"destructive",size:"sm",onClick:()=>y(e.id,"Purchase"),children:(0,a.jsx)(j.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Amount:"}),(0,a.jsxs)("p",{className:"font-medium",children:["₹",e.amount]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Status:"}),(0,a.jsx)(l.E,{variant:"default",children:e.status})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Payment ID:"}),(0,a.jsx)("p",{className:"font-mono text-xs",children:e.razorpay_payment_id})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Date:"}),(0,a.jsx)("p",{children:new Date(e.created_at).toLocaleDateString()})]})]})]},e.id))})})]})}),(0,a.jsx)(d.av,{value:"customizations",className:"space-y-4",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(i.ZB,{children:"Customizations"}),(0,a.jsx)(i.BT,{children:"User template customization sessions"})]}),(0,a.jsx)("div",{className:"flex gap-2",children:(0,a.jsxs)(n.$,{variant:"outline",onClick:()=>t(g,"customizations",["User","Navbar Style","Hero Section","Footer Style","Created At","Updated At"]),children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Export CSV"]})})]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:g.map(e=>(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold",children:e.profiles.full_name}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["ID: ",e.id]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.$,{variant:"outline",size:"sm",children:(0,a.jsx)(p.A,{className:"h-4 w-4"})}),(0,a.jsx)(n.$,{variant:"destructive",size:"sm",onClick:()=>y(e.id,"Customization"),children:(0,a.jsx)(j.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Navbar:"}),(0,a.jsx)("p",{className:"font-medium",children:e.navbar_style})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Hero:"}),(0,a.jsx)("p",{className:"font-medium",children:e.hero_section})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Footer:"}),(0,a.jsx)("p",{className:"font-medium",children:e.footer_style})]})]}),(0,a.jsxs)("div",{className:"mt-2 text-xs text-muted-foreground",children:["Created: ",new Date(e.created_at).toLocaleString()," • Updated: ",new Date(e.updated_at).toLocaleString()]})]},e.id))})})]})}),(0,a.jsx)(d.av,{value:"visitors",className:"space-y-4",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(i.ZB,{children:"Visitor Logs"}),(0,a.jsxs)(i.BT,{children:[N.length," visits • ",new Set(N.map(e=>e.ip_address)).size," unique IPs"]})]}),(0,a.jsx)("div",{className:"flex gap-2",children:(0,a.jsxs)(n.$,{variant:"outline",onClick:()=>t(N,"visitor-logs",["IP Address","Path","User Agent","Created At"]),children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Export CSV"]})})]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:N.map(e=>(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold",children:e.path}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["IP: ",e.ip_address]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.$,{variant:"outline",size:"sm",children:(0,a.jsx)(p.A,{className:"h-4 w-4"})}),(0,a.jsx)(n.$,{variant:"destructive",size:"sm",onClick:()=>y(e.id,"Visitor log"),children:(0,a.jsx)(j.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("p",{className:"text-muted-foreground mb-1",children:"User Agent:"}),(0,a.jsx)("p",{className:"font-mono text-xs break-all",children:e.user_agent})]}),(0,a.jsx)("div",{className:"mt-2 text-xs text-muted-foreground",children:new Date(e.created_at).toLocaleString()})]},e.id))})})]})})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71057:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("shopping-bag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88233:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[447,529,658,97,391,685],()=>t(8386));module.exports=a})();